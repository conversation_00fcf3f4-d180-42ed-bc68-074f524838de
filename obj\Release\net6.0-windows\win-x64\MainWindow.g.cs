﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8A4ED532D180EFE33C8F5FC9199BF5AF791BF8C1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateTimeBlock;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardBtn;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DevicesBtn;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesBtn;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomersBtn;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SuppliersBtn;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShipmentsBtn;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaintenanceBtn;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InstallationsBtn;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SparePartsBtn;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ContentArea;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DateTimeBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DashboardBtn = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\MainWindow.xaml"
            this.DashboardBtn.Click += new System.Windows.RoutedEventHandler(this.DashboardBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DevicesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\MainWindow.xaml"
            this.DevicesBtn.Click += new System.Windows.RoutedEventHandler(this.DevicesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\MainWindow.xaml"
            this.InventoryBtn.Click += new System.Windows.RoutedEventHandler(this.InventoryBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SalesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\MainWindow.xaml"
            this.SalesBtn.Click += new System.Windows.RoutedEventHandler(this.SalesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CustomersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\MainWindow.xaml"
            this.CustomersBtn.Click += new System.Windows.RoutedEventHandler(this.CustomersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SuppliersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\MainWindow.xaml"
            this.SuppliersBtn.Click += new System.Windows.RoutedEventHandler(this.SuppliersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ShipmentsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\MainWindow.xaml"
            this.ShipmentsBtn.Click += new System.Windows.RoutedEventHandler(this.ShipmentsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.MaintenanceBtn = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\MainWindow.xaml"
            this.MaintenanceBtn.Click += new System.Windows.RoutedEventHandler(this.MaintenanceBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InstallationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\MainWindow.xaml"
            this.InstallationsBtn.Click += new System.Windows.RoutedEventHandler(this.InstallationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SparePartsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\MainWindow.xaml"
            this.SparePartsBtn.Click += new System.Windows.RoutedEventHandler(this.SparePartsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\MainWindow.xaml"
            this.SettingsBtn.Click += new System.Windows.RoutedEventHandler(this.SettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ContentArea = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

