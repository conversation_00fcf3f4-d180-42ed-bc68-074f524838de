using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Windows;
using MedicalDevicesManager.Controls;
using System.Windows.Documents;

namespace MedicalDevicesManager
{
    public partial class MainWindow : Window
    {
        private Button activeButton;
        private DispatcherTimer timer;
        
        public MainWindow()
        {
            InitializeComponent();
            InitializeTimer();
            SetActiveButton(DashboardBtn);
            UpdateDatabaseSchema();
            ShowDashboardAsync();
        }
        
        private void UpdateDatabaseSchema()
        {
            try
            {
                using (var context = new MedicalDevicesContext())
                {
                    // التأكد من وجود قاعدة البيانات
                    context.Database.EnsureCreated();

                    // محاولة إضافة العمود للصيانة
                    try
                    {
                        context.Database.ExecuteSqlRaw(@"
                            ALTER TABLE MaintenanceRecords ADD COLUMN ReportBookletPath TEXT DEFAULT '';
                        ");
                    }
                    catch
                    {
                        // العمود موجود بالفعل
                    }

                    // محاولة إضافة العمود للتنصيبات - استخدام اسم الجدول الصحيح
                    try
                    {
                        context.Database.ExecuteSqlRaw(@"
                            ALTER TABLE DeviceInstallations ADD COLUMN InstallationReportBookletPath TEXT DEFAULT '';
                        ");
                    }
                    catch
                    {
                        // العمود موجود بالفعل
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تحذير: مشكلة في تحديث قاعدة البيانات: {ex.Message}", "تحذير",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();
        }
        
        private void Timer_Tick(object sender, EventArgs e)
        {
            DateTimeBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        }
        
        private void DashboardBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(DashboardBtn);
            ShowDashboardAsync();
        }
        
        private void DevicesBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(DevicesBtn);
            ShowDevicesAsync();
        }
        
        private void InventoryBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(InventoryBtn);
            ShowInventoryAsync();
        }
        
        private void SalesBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(SalesBtn);
            ShowSalesAsync();
        }
        
        private void CustomersBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(CustomersBtn);
            ShowCustomersAsync();
        }
        
        private void SuppliersBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(SuppliersBtn);
            ShowSuppliersAsync();
        }
        
        private void ShipmentsBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(ShipmentsBtn);
            ShowShipmentsAsync();
        }
        
        private void MaintenanceBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(MaintenanceBtn);
            ShowMaintenanceAsync();
        }

        private void InstallationsBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(InstallationsBtn);
            ShowInstallationsAsync();
        }

        private void SparePartsBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(SparePartsBtn);
            ShowSparePartsAsync();
        }
        
        private void SettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            SetActiveButton(SettingsBtn);
            var settingsWindow = new SettingsWindow();
            settingsWindow.ShowDialog();
        }

        // تم نقل وظيفة تشخيص النظام إلى نافذة الإعدادات

        // تم نقل الوظائف التالية إلى نافذة الإعدادات:
        // - الإشعارات والتنبيهات
        // - النسخ الاحتياطي
        // - تصدير البيانات
        // - إعدادات المخزون المتقدمة
        
        private void SetActiveButton(Button button)
        {
            if (activeButton != null)
                activeButton.Background = Brushes.Transparent;
            
            button.Background = new SolidColorBrush(Color.FromRgb(0, 123, 255));
            activeButton = button;
        }
        
        private async void ShowDashboardAsync()
        {
            var content = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
            };

            var mainPanel = new StackPanel
            {
                Margin = new Thickness(20)
            };

            // العنوان الرئيسي
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 30)
            };

            var titleIcon = new TextBlock
            {
                Text = "📊",
                FontSize = 36,
                Margin = new Thickness(0, 0, 15, 0)
            };

            var titleText = new TextBlock
            {
                Text = "لوحة التحكم الرئيسية",
                FontSize = 32,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };

            titlePanel.Children.Add(titleIcon);
            titlePanel.Children.Add(titleText);
            mainPanel.Children.Add(titlePanel);

            // الإحصائيات السريعة
            var statsTitle = new TextBlock
            {
                Text = "📈 الإحصائيات السريعة",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            mainPanel.Children.Add(statsTitle);

            // شبكة الإحصائيات
            var statsGrid = new UniformGrid
            {
                Columns = 3,
                Rows = 2,
                Margin = new Thickness(0, 0, 0, 30)
            };

            try
            {
                // حساب الإحصائيات الحقيقية
                var salesStats = await CalculateSalesStatsAsync();
                var inventoryStats = await CalculateInventoryStatsAsync();
                var customerStats = await CalculateCustomerStatsAsync();
                var maintenanceStats = await CalculateMaintenanceStatsAsync();
                var shipmentStats = await CalculateShipmentStatsAsync();
                var profitStats = await CalculateProfitStatsAsync();

                // إضافة بطاقات الإحصائيات
                statsGrid.Children.Add(CreateInteractiveStatsCard("💰", "إجمالي المبيعات", salesStats.TotalSales, salesStats.SalesChange, "#28A745", () => ShowSalesAsync()));
                statsGrid.Children.Add(CreateInteractiveStatsCard("📦", "حالة المخزون", $"{inventoryStats.AvailableItems} متاح", inventoryStats.LowStockItems > 0 ? $"{inventoryStats.LowStockItems} منخفض" : "مستقر", "#17A2B8", () => ShowInventoryAsync()));
                statsGrid.Children.Add(CreateInteractiveStatsCard("👥", "العملاء النشطون", $"{customerStats.ActiveCustomers}", $"{customerStats.NewCustomers} جديد", "#FFC107", () => ShowCustomersAsync()));
                statsGrid.Children.Add(CreateInteractiveStatsCard("🛠️", "الصيانة المعلقة", $"{maintenanceStats.PendingMaintenance}", maintenanceStats.OverdueMaintenance > 0 ? $"{maintenanceStats.OverdueMaintenance} متأخر" : "محدث", "#6F42C1", () => ShowMaintenanceAsync()));
                statsGrid.Children.Add(CreateInteractiveStatsCard("🚚", "الشحنات قيد التنفيذ", $"{shipmentStats.InTransitShipments}", $"{shipmentStats.DeliveredToday} تم تسليمها اليوم", "#FD7E14", () => ShowShipmentsAsync()));
                statsGrid.Children.Add(CreateInteractiveStatsCard("💵", "الأرباح الصافية", profitStats.NetProfit, profitStats.ProfitMargin, "#DC3545", () => { var reportsWindow = new ReportsWindow(); reportsWindow.ShowDialog(); }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            mainPanel.Children.Add(statsGrid);

            // الاختصارات السريعة
            var shortcutsTitle = new TextBlock
            {
                Text = "⚡ الاختصارات السريعة",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            mainPanel.Children.Add(shortcutsTitle);

            var shortcutsPanel = new UniformGrid
            {
                Columns = 4,
                Margin = new Thickness(0, 0, 0, 30)
            };

            shortcutsPanel.Children.Add(CreateShortcutButton("💰", "بيع جديد", "#28A745", () => { var addWindow = new AddEditSaleWindow(); addWindow.ShowDialog(); }));
            shortcutsPanel.Children.Add(CreateShortcutButton("👥", "عميل جديد", "#FFC107", () => { var addWindow = new AddEditCustomerWindow(); addWindow.ShowDialog(); }));
            shortcutsPanel.Children.Add(CreateShortcutButton("🏥", "جهاز جديد", "#007BFF", () => { var addWindow = new AddEditDeviceWindow(); addWindow.ShowDialog(); }));
            shortcutsPanel.Children.Add(CreateShortcutButton("📋", "إدارة الفئات", "#6F42C1", () => { var categoriesWindow = new ManageCategoriesWindow(); categoriesWindow.ShowDialog(); }));

            mainPanel.Children.Add(shortcutsPanel);

            // الأنشطة الأخيرة
            var activitiesTitle = new TextBlock
            {
                Text = "📋 الأنشطة الأخيرة",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            mainPanel.Children.Add(activitiesTitle);

            try
            {
                var recentActivities = await GetRecentActivitiesAsync();
                var activitiesPanel = CreateRecentActivitiesPanel(recentActivities);
                mainPanel.Children.Add(activitiesPanel);
            }
            catch (Exception ex)
            {
                var errorText = new TextBlock
                {
                    Text = $"خطأ في تحميل الأنشطة الأخيرة: {ex.Message}",
                    Foreground = Brushes.Red,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                mainPanel.Children.Add(errorText);
            }

            content.Content = mainPanel;
            ContentArea.Content = content;
        }

        private async void ShowDevicesAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🏥 إدارة الأجهزة الطبية",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // شريط الأدوات العلوي
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "➕ إضافة جهاز جديد",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddDeviceButton_Click;

            actionsPanel.Children.Add(addButton);
            content.Children.Add(actionsPanel);

            // إنشاء الجدول المتقدم
            var advancedGrid = new AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableColumnFreeze = true,
                EnableRowNumbers = true,
                EnableMultiSelect = true,
                EnableQuickPreview = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                Margin = new Thickness(0, 0, 0, 20)
            };

            // إعداد الأعمدة
            advancedGrid.AddTextColumn("الاسم", "Name", 150);
            advancedGrid.AddTextColumn("الماركة", "Brand", 120);
            advancedGrid.AddTextColumn("الموديل", "Model", 120);
            advancedGrid.AddTextColumn("الرقم التسلسلي", "SerialNumber", 130);
            advancedGrid.AddTextColumn("الفئة", "Category", 120);
            advancedGrid.AddCurrencyColumn("سعر الشراء", "PurchasePrice", 130);
            advancedGrid.AddCurrencyColumn("سعر البيع", "SellingPrice", 130);
            advancedGrid.AddDateColumn("تاريخ الشراء", "PurchaseDate", "yyyy/MM/dd", 130);
            advancedGrid.AddDateColumn("نهاية الضمان", "WarrantyEndDate", "yyyy/MM/dd", 130);
            advancedGrid.AddTextColumn("المورد", "Supplier", 150);
            advancedGrid.AddTextColumn("الموقع", "Location", 120);
            advancedGrid.AddTextColumn("الحالة", "Status", 100);

            // إضافة عمود الإجراءات
            var actions = new List<ActionButton>
            {
                new ActionButton
                {
                    Icon = "👁️",
                    Tooltip = "عرض التفاصيل",
                    BackgroundColor = Color.FromRgb(23, 162, 184),
                    ClickHandler = ViewDeviceDetails_Click
                },
                new ActionButton
                {
                    Icon = "✏️",
                    Tooltip = "تعديل الجهاز",
                    BackgroundColor = Color.FromRgb(255, 193, 7),
                    ClickHandler = EditDeviceButton_Click
                },

                new ActionButton
                {
                    Icon = "🗑️",
                    Tooltip = "حذف الجهاز",
                    BackgroundColor = Color.FromRgb(220, 53, 69),
                    ClickHandler = DeleteDeviceButton_Click
                }
            };

            advancedGrid.AddActionsColumn("الإجراءات", actions, 160);

            // إعداد الأحداث
            advancedGrid.RowDoubleClick += (sender, item) =>
            {
                if (item is MedicalDevice device)
                {
                    var button = new Button { DataContext = device };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    ViewDeviceDetails_Click(button, args);
                }
            };

            advancedGrid.RowSelected += (sender, item) =>
            {
                // يمكن إضافة معاينة سريعة هنا
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowDevicesAsync();
            };

            advancedGrid.DeleteRequested += (sender, item) =>
            {
                if (item is MedicalDevice device)
                {
                    var button = new Button { DataContext = device };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    DeleteDeviceButton_Click(button, args);
                }
            };

            try
            {
                // تحميل الأجهزة الطبية فقط (وليس الأرقام التسلسلية)
                var devices = await App.DatabaseContext.MedicalDevices
                    .OrderByDescending(d => d.CreatedDate)
                    .ToListAsync();

                advancedGrid.SetDataSource(devices);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأجهزة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowInventoryAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "📦 إدارة المخزون",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // شريط الأدوات العلوي
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "➕ إضافة عنصر جديد",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddInventoryButton_Click;

            actionsPanel.Children.Add(addButton);
            content.Children.Add(actionsPanel);

            // إنشاء الجدول المتقدم
            var advancedGrid = new AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 30,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableColumnFreeze = true,
                EnableRowNumbers = true,
                EnableMultiSelect = true,
                EnableQuickPreview = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                Margin = new Thickness(0, 0, 0, 20)
            };

            // إعداد الأعمدة
            advancedGrid.AddTextColumn("اسم العنصر", "Name", 160);
            advancedGrid.AddTextColumn("الفئة", "Category", 120);
            advancedGrid.AddNumericColumn("المخزون الحالي", "CurrentStock", "N0", 110);
            advancedGrid.AddNumericColumn("الحد الأدنى", "MinimumStock", "N0", 100);
            advancedGrid.AddTextColumn("الوحدة", "Unit", 80);
            advancedGrid.AddCurrencyColumn("سعر الوحدة", "UnitPrice", 120);
            advancedGrid.AddTextColumn("الموقع", "Location", 120);
            advancedGrid.AddTextColumn("الحالة", "Status", 100);
            advancedGrid.AddDateColumn("آخر تحديث", "LastUpdated", "yyyy/MM/dd", 120);

            // إضافة عمود الإجراءات
            var actions = new List<ActionButton>
            {
                new ActionButton
                {
                    Icon = "👁️",
                    Tooltip = "عرض التفاصيل",
                    BackgroundColor = Color.FromRgb(23, 162, 184),
                    ClickHandler = ViewInventoryDetails_Click
                },
                new ActionButton
                {
                    Icon = "✏️",
                    Tooltip = "تعديل العنصر",
                    BackgroundColor = Color.FromRgb(255, 193, 7),
                    ClickHandler = EditInventoryButton_Click
                },
                new ActionButton
                {
                    Icon = "📊",
                    Tooltip = "تقرير المخزون",
                    BackgroundColor = Color.FromRgb(108, 117, 125),
                    ClickHandler = InventoryReport_Click
                },
                new ActionButton
                {
                    Icon = "🗑️",
                    Tooltip = "حذف العنصر",
                    BackgroundColor = Color.FromRgb(220, 53, 69),
                    ClickHandler = DeleteInventoryButton_Click
                }
            };

            advancedGrid.AddActionsColumn("الإجراءات", actions, 160);

            // إعداد الأحداث
            advancedGrid.RowDoubleClick += (sender, item) =>
            {
                if (item is InventoryItem inventoryItem)
                {
                    var button = new Button { DataContext = inventoryItem };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    ViewInventoryDetails_Click(button, args);
                }
            };

            advancedGrid.RowSelected += (sender, item) =>
            {
                // يمكن إضافة معاينة سريعة هنا
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowInventoryAsync();
            };

            advancedGrid.DeleteRequested += (sender, item) =>
            {
                if (item is InventoryItem inventoryItem)
                {
                    var button = new Button { DataContext = inventoryItem };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    DeleteInventoryButton_Click(button, args);
                }
            };

            try
            {
                var items = await App.DatabaseContext.InventoryItems.ToListAsync();
                advancedGrid.SetDataSource(items);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowSalesAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "💰 إدارة المبيعات",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // شريط الأدوات العلوي
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "💰 بيع جديد",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddSaleButton_Click;

            actionsPanel.Children.Add(addButton);
            content.Children.Add(actionsPanel);

            // إنشاء الجدول المتقدم
            var advancedGrid = new AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 20,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableColumnFreeze = true,
                EnableRowNumbers = true,
                EnableMultiSelect = true,
                EnableQuickPreview = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                Margin = new Thickness(0, 0, 0, 20)
            };

            // إعداد الأعمدة
            advancedGrid.AddTextColumn("رقم الفاتورة", "InvoiceNumber", 120);
            advancedGrid.AddDateColumn("التاريخ", "SaleDate", "dd/MM/yyyy", 100);
            advancedGrid.AddTextColumn("العميل", "CustomerName", 150);
            advancedGrid.AddTextColumn("الجهاز", "DeviceName", 180);
            advancedGrid.AddNumericColumn("الكمية", "Quantity", "N0", 80);
            advancedGrid.AddCurrencyColumn("المبلغ النهائي", "FinalAmount", 120);
            advancedGrid.AddTextColumn("حالة الدفع", "PaymentStatus", 100);
            advancedGrid.AddTextColumn("طريقة الدفع", "PaymentMethod", 100);

            // إضافة عمود الإجراءات
            var actions = new List<ActionButton>
            {
                new ActionButton
                {
                    Icon = "👁️",
                    Tooltip = "عرض تفاصيل الفاتورة",
                    BackgroundColor = Color.FromRgb(23, 162, 184),
                    ClickHandler = ViewSaleDetails_Click
                },
                new ActionButton
                {
                    Icon = "✏️",
                    Tooltip = "تعديل المبيعة",
                    BackgroundColor = Color.FromRgb(255, 193, 7),
                    ClickHandler = EditSaleButton_Click
                },
                new ActionButton
                {
                    Icon = "🧾",
                    Tooltip = "طباعة الفاتورة",
                    BackgroundColor = Color.FromRgb(108, 117, 125),
                    ClickHandler = PrintInvoice_Click
                },
                new ActionButton
                {
                    Icon = "🗑️",
                    Tooltip = "حذف المبيعة",
                    BackgroundColor = Color.FromRgb(220, 53, 69),
                    ClickHandler = DeleteSaleButton_Click
                }
            };

            advancedGrid.AddActionsColumn("الإجراءات", actions, 160);

            // إعداد الأحداث
            advancedGrid.RowDoubleClick += (sender, item) =>
            {
                if (item is Sale sale)
                {
                    var button = new Button { DataContext = sale };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    ViewSaleDetails_Click(button, args);
                }
            };

            advancedGrid.RowSelected += (sender, item) =>
            {
                // يمكن إضافة معاينة سريعة هنا
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowSalesAsync();
            };

            advancedGrid.DeleteRequested += (sender, item) =>
            {
                if (item is Sale sale)
                {
                    var button = new Button { DataContext = sale };
                    var args = new RoutedEventArgs(Button.ClickEvent, button);
                    DeleteSaleButton_Click(button, args);
                }
            };

            try
            {
                var sales = await App.DatabaseContext.Sales.ToListAsync();
                advancedGrid.SetDataSource(sales);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowCustomersAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "👥 إدارة العملاء",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "👥 إضافة عميل جديد",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddCustomerButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowCustomersAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم للعملاء
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "اسم العميل", Binding = new System.Windows.Data.Binding("Name"), Width = 150 },
                new DataGridTextColumn { Header = "نوع العميل", Binding = new System.Windows.Data.Binding("CustomerType"), Width = 100 },
                new DataGridTextColumn { Header = "رقم الهاتف", Binding = new System.Windows.Data.Binding("Phone"), Width = 110 },
                new DataGridTextColumn { Header = "البريد الإلكتروني", Binding = new System.Windows.Data.Binding("Email"), Width = 160 },
                new DataGridTextColumn { Header = "المدينة", Binding = new System.Windows.Data.Binding("City"), Width = 90 },
                new DataGridTextColumn { Header = "الحد الائتماني", Binding = new System.Windows.Data.Binding("CreditLimit") { StringFormat = "{0:C}" }, Width = 110 },
                new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 80 }
            };

            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, customer) =>
            {
                var editWindow = new AddEditCustomerWindow(customer as Customer);
                if (editWindow.ShowDialog() == true)
                {
                    ShowCustomersAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, customer) =>
            {
                var customerObj = customer as Customer;
                if (customerObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف العميل '{customerObj.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.Customers.Remove(customerObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف العميل بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowCustomersAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف العميل: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, customer) =>
            {
                var customerObj = customer as Customer;
                if (customerObj != null)
                {
                    MessageBox.Show($"تفاصيل العميل:\n\n" +
                                   $"الاسم: {customerObj.Name}\n" +
                                   $"النوع: {customerObj.CustomerType}\n" +
                                   $"الهاتف: {customerObj.Phone}\n" +
                                   $"البريد: {customerObj.Email}\n" +
                                   $"المدينة: {customerObj.City}\n" +
                                   $"الحد الائتماني: {customerObj.CreditLimit:C}\n" +
                                   $"الحالة: {customerObj.Status}",
                                   "تفاصيل العميل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowCustomersAsync();
            };

            try
            {
                var customers = await App.DatabaseContext.Customers.ToListAsync();
                advancedGrid.SetDataSource(customers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowSuppliersAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🏭 إدارة الموردين",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "🏭 إضافة مورد جديد",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddSupplierButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowSuppliersAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم للموردين
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "اسم المورد", Binding = new System.Windows.Data.Binding("Name"), Width = 150 },
                new DataGridTextColumn { Header = "رقم الهاتف", Binding = new System.Windows.Data.Binding("Phone"), Width = 110 },
                new DataGridTextColumn { Header = "البريد الإلكتروني", Binding = new System.Windows.Data.Binding("Email"), Width = 160 },
                new DataGridTextColumn { Header = "المدينة", Binding = new System.Windows.Data.Binding("City"), Width = 90 },
                new DataGridTextColumn { Header = "التقييم", Binding = new System.Windows.Data.Binding("Rating") { StringFormat = "{0:F1}" }, Width = 70 },
                new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 80 }
            };

            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, supplier) =>
            {
                var editWindow = new AddEditSupplierWindow(supplier as Supplier);
                if (editWindow.ShowDialog() == true)
                {
                    ShowSuppliersAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, supplier) =>
            {
                var supplierObj = supplier as Supplier;
                if (supplierObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المورد '{supplierObj.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.Suppliers.Remove(supplierObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف المورد بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowSuppliersAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف المورد: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, supplier) =>
            {
                var supplierObj = supplier as Supplier;
                if (supplierObj != null)
                {
                    MessageBox.Show($"تفاصيل المورد:\n\n" +
                                   $"الاسم: {supplierObj.Name}\n" +
                                   $"الهاتف: {supplierObj.Phone}\n" +
                                   $"البريد: {supplierObj.Email}\n" +
                                   $"المدينة: {supplierObj.City}\n" +
                                   $"التقييم: {supplierObj.Rating:F1}\n" +
                                   $"الحالة: {supplierObj.Status}",
                                   "تفاصيل المورد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowSuppliersAsync();
            };

            try
            {
                var suppliers = await App.DatabaseContext.Suppliers.ToListAsync();
                advancedGrid.SetDataSource(suppliers);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowShipmentsAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🚚 إدارة الشحنات",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "🚚 إضافة شحنة جديدة",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddShipmentButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowShipmentsAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم للشحنات
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "رقم الشحنة", Binding = new System.Windows.Data.Binding("ShipmentNumber"), Width = 110 },
                new DataGridTextColumn { Header = "تاريخ الشحن", Binding = new System.Windows.Data.Binding("ShipmentDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 90 },
                new DataGridTextColumn { Header = "تاريخ الاستلام", Binding = new System.Windows.Data.Binding("ReceiptDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 90 },
                new DataGridTextColumn { Header = "المستلم", Binding = new System.Windows.Data.Binding("RecipientName"), Width = 130 },
                new DataGridTextColumn { Header = "عنوان التسليم", Binding = new System.Windows.Data.Binding("DeliveryAddress"), Width = 180 },
                new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 90 },
                new DataGridTextColumn { Header = "قيمة الشحنة", Binding = new System.Windows.Data.Binding("TotalValue") { StringFormat = "{0:C}" }, Width = 90 },
                new DataGridTextColumn { Header = "تكلفة الشحن", Binding = new System.Windows.Data.Binding("ShippingCost") { StringFormat = "{0:C}" }, Width = 90 },
                new DataGridTextColumn { Header = "رقم التتبع", Binding = new System.Windows.Data.Binding("TrackingNumber"), Width = 100 }
            };

            // عمود أوراق الشحنة مع عرض اسم الملف فقط
            var documentsColumn = new DataGridTemplateColumn
            {
                Header = "أوراق الشحنة",
                Width = 120
            };

            var documentsTemplate = new DataTemplate();
            var documentsTextBlock = new FrameworkElementFactory(typeof(TextBlock));
            documentsTextBlock.SetBinding(TextBlock.TextProperty, new System.Windows.Data.Binding("ShipmentDocumentsPath")
            {
                Converter = new FileNameConverter()
            });
            documentsTextBlock.SetValue(TextBlock.TextAlignmentProperty, TextAlignment.Center);
            documentsTemplate.VisualTree = documentsTextBlock;
            documentsColumn.CellTemplate = documentsTemplate;

            columns.Add(documentsColumn);
            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, shipment) =>
            {
                var editWindow = new AddEditShipmentWindow(shipment as Shipment);
                if (editWindow.ShowDialog() == true)
                {
                    ShowShipmentsAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, shipment) =>
            {
                var shipmentObj = shipment as Shipment;
                if (shipmentObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الشحنة '{shipmentObj.ShipmentNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.Shipments.Remove(shipmentObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف الشحنة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowShipmentsAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف الشحنة: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, shipment) =>
            {
                var shipmentObj = shipment as Shipment;
                if (shipmentObj != null)
                {
                    MessageBox.Show($"تفاصيل الشحنة:\n\n" +
                                   $"رقم الشحنة: {shipmentObj.ShipmentNumber}\n" +
                                   $"تاريخ الشحن: {shipmentObj.ShipmentDate:dd/MM/yyyy}\n" +
                                   $"تاريخ الاستلام: {shipmentObj.ReceiptDate:dd/MM/yyyy}\n" +
                                   $"المستلم: {shipmentObj.RecipientName}\n" +
                                   $"عنوان التسليم: {shipmentObj.DeliveryAddress}\n" +
                                   $"الحالة: {shipmentObj.Status}\n" +
                                   $"قيمة الشحنة: {shipmentObj.TotalValue:C}\n" +
                                   $"تكلفة الشحن: {shipmentObj.ShippingCost:C}\n" +
                                   $"رقم التتبع: {shipmentObj.TrackingNumber}",
                                   "تفاصيل الشحنة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowShipmentsAsync();
            };

            try
            {
                var shipments = await App.DatabaseContext.Shipments.ToListAsync();
                advancedGrid.SetDataSource(shipments);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشحنات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowMaintenanceAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🛠️ الضمان والصيانة",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "🛠️ إضافة سجل صيانة",
                Style = (Style)FindResource("PrimaryButton"),
                Margin = new Thickness(0, 0, 10, 0)
            };
            addButton.Click += AddMaintenanceButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowMaintenanceAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم للصيانة
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "اسم الجهاز", Binding = new System.Windows.Data.Binding("DeviceName"), Width = 150 },
                new DataGridTextColumn { Header = "الرقم التسلسلي", Binding = new System.Windows.Data.Binding("SerialNumber"), Width = 100 },
                new DataGridTextColumn { Header = "نوع الصيانة", Binding = new System.Windows.Data.Binding("MaintenanceType"), Width = 100 },
                new DataGridTextColumn { Header = "تاريخ الصيانة", Binding = new System.Windows.Data.Binding("MaintenanceDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 90 },
                new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 90 },
                new DataGridTextColumn { Header = "الصيانة القادمة", Binding = new System.Windows.Data.Binding("NextMaintenanceDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 90 },
                new DataGridTextColumn { Header = "التكلفة", Binding = new System.Windows.Data.Binding("Cost") { StringFormat = "{0:C}" }, Width = 80 },
                new DataGridTextColumn { Header = "اسم الفني", Binding = new System.Windows.Data.Binding("TechnicianName"), Width = 100 },
                new DataGridTextColumn { Header = "الوصف", Binding = new System.Windows.Data.Binding("Description"), Width = 160 }
            };

            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, maintenance) =>
            {
                var editWindow = new AddEditMaintenanceWindow(maintenance as MaintenanceRecord);
                if (editWindow.ShowDialog() == true)
                {
                    ShowMaintenanceAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, maintenance) =>
            {
                var maintenanceObj = maintenance as MaintenanceRecord;
                if (maintenanceObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف سجل الصيانة للجهاز '{maintenanceObj.DeviceName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.MaintenanceRecords.Remove(maintenanceObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف سجل الصيانة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowMaintenanceAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف سجل الصيانة: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, maintenance) =>
            {
                var maintenanceObj = maintenance as MaintenanceRecord;
                if (maintenanceObj != null)
                {
                    var detailsWindow = new MaintenanceDetailsWindow(maintenanceObj);
                    detailsWindow.ShowDialog();
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowMaintenanceAsync();
            };

            try
            {
                var maintenance = await App.DatabaseContext.MaintenanceRecords.ToListAsync();
                advancedGrid.SetDataSource(maintenance);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجلات الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private async void ShowInstallationsAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🔧 إدارة تنصيب الأجهزة الطبية",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // شريط الأدوات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "➕ إضافة تنصيب جديد",
                Width = 150,
                Height = 35,
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 10, 0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            addButton.Click += AddInstallationButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Width = 100,
                Height = 35,
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowInstallationsAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم للتنصيبات
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة الأساسية
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "اسم الجهاز", Binding = new System.Windows.Data.Binding("DeviceName"), Width = 150 },
                new DataGridTextColumn { Header = "الرقم التسلسلي", Binding = new System.Windows.Data.Binding("SerialNumber"), Width = 120 },
                new DataGridTextColumn { Header = "الموديل", Binding = new System.Windows.Data.Binding("Model"), Width = 100 },
                new DataGridTextColumn { Header = "الشركة المصنعة", Binding = new System.Windows.Data.Binding("Manufacturer"), Width = 120 },
                new DataGridTextColumn { Header = "العميل", Binding = new System.Windows.Data.Binding("CustomerName"), Width = 150 },
                new DataGridTextColumn { Header = "تاريخ التنصيب", Binding = new System.Windows.Data.Binding("InstallationDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 100 },
                new DataGridTextColumn { Header = "سنوات الضمان", Binding = new System.Windows.Data.Binding("WarrantyYears"), Width = 80 },
                new DataGridTextColumn { Header = "انتهاء الضمان", Binding = new System.Windows.Data.Binding("WarrantyEndDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 100 },
                new DataGridTextColumn { Header = "موقع التنصيب", Binding = new System.Windows.Data.Binding("InstallationLocation"), Width = 120 },
                new DataGridTextColumn { Header = "الشركة المجهزة", Binding = new System.Windows.Data.Binding("SupplierCompany"), Width = 120 },
                new DataGridTextColumn { Header = "تكلفة التنصيب", Binding = new System.Windows.Data.Binding("InstallationCost") { StringFormat = "{0:N0} د.ع" }, Width = 100 }
            };

            // عمود كتيب تقرير التنصيب
            var reportColumn = new DataGridTemplateColumn
            {
                Header = "كتيب التقرير",
                Width = 120
            };

            var reportTemplate = new DataTemplate();
            var reportButton = new FrameworkElementFactory(typeof(Button));
            reportButton.SetValue(Button.ContentProperty, "📄 عرض");
            reportButton.SetValue(Button.WidthProperty, 80.0);
            reportButton.SetValue(Button.HeightProperty, 25.0);
            reportButton.SetValue(Button.BackgroundProperty, new SolidColorBrush(Color.FromRgb(23, 162, 184)));
            reportButton.SetValue(Button.ForegroundProperty, Brushes.White);
            reportButton.SetValue(Button.BorderThicknessProperty, new Thickness(0));
            reportButton.SetValue(Button.FontSizeProperty, 10.0);
            reportButton.SetValue(Button.CursorProperty, System.Windows.Input.Cursors.Hand);
            reportButton.SetValue(Button.ToolTipProperty, "عرض كتيب تقرير التنصيب");
            reportButton.AddHandler(Button.ClickEvent, new RoutedEventHandler(ViewInstallationReportButton_Click));

            // إخفاء الزر إذا لم يكن هناك كتيب
            var reportTrigger = new DataTrigger
            {
                Binding = new System.Windows.Data.Binding("InstallationReportBookletPath"),
                Value = ""
            };
            reportTrigger.Setters.Add(new Setter(Button.IsEnabledProperty, false));
            reportTrigger.Setters.Add(new Setter(Button.ContentProperty, "لا يوجد"));
            reportTrigger.Setters.Add(new Setter(Button.BackgroundProperty, new SolidColorBrush(Color.FromRgb(108, 117, 125))));

            var reportStyle = new Style(typeof(Button));
            reportStyle.Triggers.Add(reportTrigger);
            reportButton.SetValue(Button.StyleProperty, reportStyle);

            reportTemplate.VisualTree = reportButton;
            reportColumn.CellTemplate = reportTemplate;
            columns.Add(reportColumn);

            // عمود الحالة مع ألوان
            var statusColumn = new DataGridTemplateColumn
            {
                Header = "الحالة",
                Width = 80
            };

            var statusTemplate = new DataTemplate();
            var statusPanel = new FrameworkElementFactory(typeof(TextBlock));
            statusPanel.SetBinding(TextBlock.TextProperty, new System.Windows.Data.Binding("InstallationStatus"));
            statusPanel.SetValue(TextBlock.HorizontalAlignmentProperty, HorizontalAlignment.Center);
            statusPanel.SetValue(TextBlock.FontWeightProperty, FontWeights.Bold);
            statusPanel.SetValue(TextBlock.PaddingProperty, new Thickness(8, 4, 8, 4));
            statusPanel.SetValue(TextBlock.MarginProperty, new Thickness(2));

            var statusTrigger1 = new DataTrigger { Binding = new System.Windows.Data.Binding("InstallationStatus"), Value = "مكتمل" };
            statusTrigger1.Setters.Add(new Setter(TextBlock.ForegroundProperty, new SolidColorBrush(Color.FromRgb(40, 167, 69))));
            statusTrigger1.Setters.Add(new Setter(TextBlock.BackgroundProperty, new SolidColorBrush(Color.FromRgb(212, 237, 218))));

            var statusTrigger2 = new DataTrigger { Binding = new System.Windows.Data.Binding("InstallationStatus"), Value = "قيد التنصيب" };
            statusTrigger2.Setters.Add(new Setter(TextBlock.ForegroundProperty, new SolidColorBrush(Color.FromRgb(255, 193, 7))));
            statusTrigger2.Setters.Add(new Setter(TextBlock.BackgroundProperty, new SolidColorBrush(Color.FromRgb(255, 243, 205))));

            var statusTrigger3 = new DataTrigger { Binding = new System.Windows.Data.Binding("InstallationStatus"), Value = "مؤجل" };
            statusTrigger3.Setters.Add(new Setter(TextBlock.ForegroundProperty, new SolidColorBrush(Color.FromRgb(220, 53, 69))));
            statusTrigger3.Setters.Add(new Setter(TextBlock.BackgroundProperty, new SolidColorBrush(Color.FromRgb(248, 215, 218))));

            var statusStyle = new Style(typeof(TextBlock));
            statusStyle.Triggers.Add(statusTrigger1);
            statusStyle.Triggers.Add(statusTrigger2);
            statusStyle.Triggers.Add(statusTrigger3);
            statusPanel.SetValue(TextBlock.StyleProperty, statusStyle);

            statusTemplate.VisualTree = statusPanel;
            statusColumn.CellTemplate = statusTemplate;
            columns.Add(statusColumn);

            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, installation) =>
            {
                var editWindow = new AddEditInstallationWindow(installation as DeviceInstallation);
                if (editWindow.ShowDialog() == true)
                {
                    ShowInstallationsAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, installation) =>
            {
                var installationObj = installation as DeviceInstallation;
                if (installationObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف تنصيب الجهاز '{installationObj.DeviceName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.DeviceInstallations.Remove(installationObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف التنصيب بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowInstallationsAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف التنصيب: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, installation) =>
            {
                var installationObj = installation as DeviceInstallation;
                if (installationObj != null)
                {
                    MessageBox.Show($"تفاصيل التنصيب:\n\n" +
                                   $"اسم الجهاز: {installationObj.DeviceName}\n" +
                                   $"الرقم التسلسلي: {installationObj.SerialNumber}\n" +
                                   $"الموديل: {installationObj.Model}\n" +
                                   $"الشركة المصنعة: {installationObj.Manufacturer}\n" +
                                   $"العميل: {installationObj.CustomerName}\n" +
                                   $"تاريخ التنصيب: {installationObj.InstallationDate:dd/MM/yyyy}\n" +
                                   $"سنوات الضمان: {installationObj.WarrantyYears}\n" +
                                   $"انتهاء الضمان: {installationObj.WarrantyEndDate:dd/MM/yyyy}\n" +
                                   $"موقع التنصيب: {installationObj.InstallationLocation}\n" +
                                   $"الشركة المجهزة: {installationObj.SupplierCompany}\n" +
                                   $"تكلفة التنصيب: {installationObj.InstallationCost:N0} د.ع\n" +
                                   $"الحالة: {installationObj.InstallationStatus}",
                                   "تفاصيل التنصيب", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowInstallationsAsync();
            };

            try
            {
                var installations = await App.DatabaseContext.DeviceInstallations.ToListAsync();
                advancedGrid.SetDataSource(installations);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنصيبات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private void ShowModule(string title, string description)
        {
            var content = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 32,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 30),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };

            var descPanel = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(233, 236, 239)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(10),
                Padding = new Thickness(40, 30, 40, 30),
                MaxWidth = 700,
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 2,
                    Opacity = 0.2,
                    BlurRadius = 8
                }
            };

            var descBlock = new TextBlock
            {
                Text = description,
                FontSize = 16,
                TextAlignment = TextAlignment.Center,
                LineHeight = 24,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };

            descPanel.Child = descBlock;

            var backButton = new Button
            {
                Content = "🏠 العودة إلى لوحة التحكم",
                Style = (Style)FindResource("PrimaryButton"),
                Width = 250,
                Height = 45,
                FontSize = 16,
                Margin = new Thickness(0, 30, 0, 0)
            };

            backButton.Click += (s, e) => {
                SetActiveButton(DashboardBtn);
                ShowDashboardAsync();
            };

            content.Children.Add(titleBlock);
            content.Children.Add(descPanel);
            content.Children.Add(backButton);

            ContentArea.Content = content;
        }

        private Border CreateStatsCard(string icon, string title, string value, string color)
        {
            var card = new Border
            {
                Width = 200,
                Height = 130,
                Margin = new Thickness(15, 10, 15, 10),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(10),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 3,
                    Opacity = 0.3,
                    BlurRadius = 10
                }
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(new TextBlock
            {
                Text = icon,
                FontSize = 32,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            });

            stackPanel.Children.Add(new TextBlock
            {
                Text = title,
                FontSize = 13,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            });

            stackPanel.Children.Add(new TextBlock
            {
                Text = value,
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            });

            card.Child = stackPanel;
            return card;
        }

        private Border CreateModuleCard(string icon, string title, string color)
        {
            var card = new Border
            {
                Width = 180,
                Height = 140,
                Margin = new Thickness(15, 10, 15, 10),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(10),
                Cursor = System.Windows.Input.Cursors.Hand,
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 3,
                    Opacity = 0.3,
                    BlurRadius = 10
                }
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(new TextBlock
            {
                Text = icon,
                FontSize = 36,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 12)
            });

            stackPanel.Children.Add(new TextBlock
            {
                Text = title,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                MaxWidth = 150
            });

            card.Child = stackPanel;

            // تأثير التمرير
            card.MouseEnter += (s, e) =>
            {
                var originalColor = (Color)ColorConverter.ConvertFromString(color);
                card.Background = new SolidColorBrush(Color.FromArgb(220, originalColor.R, originalColor.G, originalColor.B));
            };

            card.MouseLeave += (s, e) =>
            {
                card.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            };

            return card;
        }

        private void AddDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditDeviceWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowDevicesAsync(); // تحديث القائمة
            }
        }

        private void ViewDeviceDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is MedicalDevice device)
                {
                    // فتح نافذة التفاصيل المتطورة
                    var detailsWindow = new Windows.DeviceDetailsWindow(device);
                    detailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تفاصيل الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void ViewInventoryDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is InventoryItem item)
            {
                var details = $"تفاصيل عنصر المخزون:\n\n" +
                             $"الاسم: {item.Name}\n" +
                             $"الفئة: {item.Category}\n" +
                             $"المخزون الحالي: {item.CurrentStock}\n" +
                             $"الحد الأدنى: {item.MinimumStock}\n" +
                             $"الوحدة: {item.Unit}\n" +
                             $"سعر الوحدة: {item.UnitPrice:C}\n" +
                             $"الموقع: {item.Location}\n" +
                             $"الحالة: {item.Status}\n" +
                             $"آخر تحديث: {item.LastUpdated:yyyy/MM/dd}";

                MessageBox.Show(details, "تفاصيل عنصر المخزون", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void InventoryReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var inventoryReportWindow = new Windows.InventoryItemReportWindow();
                inventoryReportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewSaleDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Sale sale)
            {
                var details = $"تفاصيل المبيعة:\n\n" +
                             $"رقم الفاتورة: {sale.InvoiceNumber}\n" +
                             $"التاريخ: {sale.SaleDate:dd/MM/yyyy}\n" +
                             $"العميل: {sale.CustomerName}\n" +
                             $"الجهاز: {sale.DeviceName}\n" +
                             $"الكمية: {sale.Quantity}\n" +
                             $"المبلغ النهائي: {sale.FinalAmount:C}\n" +
                             $"حالة الدفع: {sale.PaymentStatus}\n" +
                             $"طريقة الدفع: {sale.PaymentMethod}";

                MessageBox.Show(details, "تفاصيل المبيعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is Sale sale)
                {
                    var printDialog = new System.Windows.Controls.PrintDialog();
                    if (printDialog.ShowDialog() == true)
                    {
                        var document = CreateInvoiceDocument(sale);
                        printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)document).DocumentPaginator, $"فاتورة رقم {sale.InvoiceNumber}");
                        MessageBox.Show("تم إرسال الفاتورة للطباعة بنجاح", "طباعة الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private System.Windows.Documents.FlowDocument CreateInvoiceDocument(Sale sale)
        {
            var document = new System.Windows.Documents.FlowDocument();
            document.PagePadding = new Thickness(40);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 12;

            // رأس الفاتورة
            var header = new System.Windows.Documents.Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 30)
            };

            // شعار الشركة (يمكن إضافة صورة هنا)
            var companyName = new System.Windows.Documents.Run("🏥 شركة الأجهزة الطبية المتقدمة")
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.DarkBlue
            };
            header.Inlines.Add(companyName);
            header.Inlines.Add(new System.Windows.Documents.LineBreak());

            var companyInfo = new System.Windows.Documents.Run("العنوان: شارع الطب، المدينة الطبية | الهاتف: *********** | البريد: <EMAIL>")
            {
                FontSize = 10,
                Foreground = System.Windows.Media.Brushes.Gray
            };
            header.Inlines.Add(companyInfo);
            document.Blocks.Add(header);

            // خط فاصل
            var separator = new System.Windows.Documents.Paragraph()
            {
                BorderBrush = System.Windows.Media.Brushes.DarkBlue,
                BorderThickness = new Thickness(0, 0, 0, 2),
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(separator);

            // عنوان الفاتورة
            var invoiceTitle = new System.Windows.Documents.Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            var titleRun = new System.Windows.Documents.Run("🧾 فـــاتــــورة مــبــيــعــات")
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.DarkRed
            };
            invoiceTitle.Inlines.Add(titleRun);
            document.Blocks.Add(invoiceTitle);

            // معلومات الفاتورة والعميل
            var infoTable = new System.Windows.Documents.Table()
            {
                CellSpacing = 0,
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1)
            };

            // إعداد الأعمدة
            infoTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            infoTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var infoRowGroup = new System.Windows.Documents.TableRowGroup();

            // الصف الأول - رقم الفاتورة والتاريخ
            var row1 = new System.Windows.Documents.TableRow();
            var cell1 = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"رقم الفاتورة: {sale.InvoiceNumber}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(10),
                Background = System.Windows.Media.Brushes.LightBlue
            };
            var cell2 = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"التاريخ: {sale.SaleDate:dd/MM/yyyy}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(10),
                Background = System.Windows.Media.Brushes.LightBlue
            };
            row1.Cells.Add(cell1);
            row1.Cells.Add(cell2);
            infoRowGroup.Rows.Add(row1);

            // الصف الثاني - اسم العميل
            var row2 = new System.Windows.Documents.TableRow();
            var cell3 = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"اسم العميل: {sale.CustomerName}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(10),
                ColumnSpan = 2
            };
            row2.Cells.Add(cell3);
            infoRowGroup.Rows.Add(row2);

            infoTable.RowGroups.Add(infoRowGroup);
            document.Blocks.Add(infoTable);

            // مساحة فارغة
            document.Blocks.Add(new System.Windows.Documents.Paragraph() { Margin = new Thickness(0, 20, 0, 20) });

            // جدول تفاصيل المنتجات
            var productsTable = new System.Windows.Documents.Table()
            {
                CellSpacing = 0,
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1)
            };

            // إعداد أعمدة الجدول
            productsTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(50, GridUnitType.Pixel) }); // م
            productsTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(200, GridUnitType.Pixel) }); // اسم المنتج
            productsTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(80, GridUnitType.Pixel) }); // الكمية
            productsTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(100, GridUnitType.Pixel) }); // سعر الوحدة
            productsTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(100, GridUnitType.Pixel) }); // المجموع

            var productsRowGroup = new System.Windows.Documents.TableRowGroup();

            // رأس الجدول
            var headerRow = new System.Windows.Documents.TableRow();
            var headers = new[] { "م", "اسم المنتج", "الكمية", "سعر الوحدة", "المجموع" };

            foreach (var headerText in headers)
            {
                var headerCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run(headerText)))
                {
                    BorderBrush = System.Windows.Media.Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(8),
                    Background = System.Windows.Media.Brushes.DarkBlue,
                    Foreground = System.Windows.Media.Brushes.White,
                    TextAlignment = TextAlignment.Center
                };
                headerCell.Blocks.First().TextAlignment = TextAlignment.Center;
                ((System.Windows.Documents.Paragraph)headerCell.Blocks.First()).Inlines.First().FontWeight = FontWeights.Bold;
                headerRow.Cells.Add(headerCell);
            }
            productsRowGroup.Rows.Add(headerRow);

            // صف المنتج
            var productRow = new System.Windows.Documents.TableRow();

            var serialCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run("1")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };

            var nameCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"{sale.DeviceName}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8)
            };

            var quantityCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run(sale.Quantity.ToString())))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };

            var unitPriceCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"{sale.UnitPrice:C}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            };

            var totalCell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"{sale.TotalAmount:C}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center,
                Background = System.Windows.Media.Brushes.LightYellow
            };

            productRow.Cells.Add(serialCell);
            productRow.Cells.Add(nameCell);
            productRow.Cells.Add(quantityCell);
            productRow.Cells.Add(unitPriceCell);
            productRow.Cells.Add(totalCell);
            productsRowGroup.Rows.Add(productRow);

            productsTable.RowGroups.Add(productsRowGroup);
            document.Blocks.Add(productsTable);

            // مساحة فارغة
            document.Blocks.Add(new System.Windows.Documents.Paragraph() { Margin = new Thickness(0, 20, 0, 20) });

            // جدول المجاميع
            var summaryTable = new System.Windows.Documents.Table()
            {
                CellSpacing = 0,
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(300, 0, 0, 0) // محاذاة لليمين
            };

            summaryTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(150, GridUnitType.Pixel) });
            summaryTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(100, GridUnitType.Pixel) });

            var summaryRowGroup = new System.Windows.Documents.TableRowGroup();

            // المجموع الفرعي
            var subtotalRow = new System.Windows.Documents.TableRow();
            subtotalRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run("المجموع الفرعي:")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                Background = System.Windows.Media.Brushes.LightGray
            });
            subtotalRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"{sale.TotalAmount:C}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center
            });
            summaryRowGroup.Rows.Add(subtotalRow);

            // الخصم
            if (sale.DiscountAmount > 0)
            {
                var discountRow = new System.Windows.Documents.TableRow();
                discountRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"الخصم ({sale.DiscountPercentage}%):")))
                {
                    BorderBrush = System.Windows.Media.Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(8),
                    Background = System.Windows.Media.Brushes.LightGray
                });
                discountRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"-{sale.DiscountAmount:C}")))
                {
                    BorderBrush = System.Windows.Media.Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(8),
                    TextAlignment = TextAlignment.Center,
                    Foreground = System.Windows.Media.Brushes.Red
                });
                summaryRowGroup.Rows.Add(discountRow);
            }

            // المجموع النهائي
            var finalTotalRow = new System.Windows.Documents.TableRow();
            finalTotalRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run("المجموع النهائي:")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                Background = System.Windows.Media.Brushes.DarkGreen,
                Foreground = System.Windows.Media.Brushes.White
            });
            finalTotalRow.Cells.Add(new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"{sale.FinalAmount:C}")))
            {
                BorderBrush = System.Windows.Media.Brushes.Black,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(8),
                TextAlignment = TextAlignment.Center,
                Background = System.Windows.Media.Brushes.DarkGreen,
                Foreground = System.Windows.Media.Brushes.White,
                FontWeight = FontWeights.Bold
            });
            summaryRowGroup.Rows.Add(finalTotalRow);

            summaryTable.RowGroups.Add(summaryRowGroup);
            document.Blocks.Add(summaryTable);

            // معلومات الدفع
            document.Blocks.Add(new System.Windows.Documents.Paragraph() { Margin = new Thickness(0, 30, 0, 20) });

            var paymentInfo = new System.Windows.Documents.Paragraph()
            {
                Margin = new Thickness(0, 0, 0, 20)
            };
            paymentInfo.Inlines.Add(new System.Windows.Documents.Run($"طريقة الدفع: {sale.PaymentMethod}") { FontWeight = FontWeights.Bold });
            paymentInfo.Inlines.Add(new System.Windows.Documents.LineBreak());
            paymentInfo.Inlines.Add(new System.Windows.Documents.Run($"حالة الدفع: {sale.PaymentStatus}") { FontWeight = FontWeights.Bold });

            // معلومات إضافية عن الدفع
            if (sale.PaymentStatus == "مدفوع جزئياً")
            {
                paymentInfo.Inlines.Add(new System.Windows.Documents.LineBreak());
                paymentInfo.Inlines.Add(new System.Windows.Documents.Run($"المبلغ الإجمالي: {sale.FinalAmount:C}") { Foreground = System.Windows.Media.Brushes.Blue });
                paymentInfo.Inlines.Add(new System.Windows.Documents.LineBreak());
                paymentInfo.Inlines.Add(new System.Windows.Documents.Run("يرجى متابعة المبلغ المتبقي") { Foreground = System.Windows.Media.Brushes.Red, FontWeight = FontWeights.Bold });
            }

            document.Blocks.Add(paymentInfo);

            // ملاحظات
            if (!string.IsNullOrEmpty(sale.Notes))
            {
                var notesSection = new System.Windows.Documents.Paragraph()
                {
                    Margin = new Thickness(0, 20, 0, 20),
                    BorderBrush = System.Windows.Media.Brushes.Gray,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(10),
                    Background = System.Windows.Media.Brushes.LightYellow
                };
                notesSection.Inlines.Add(new System.Windows.Documents.Run("ملاحظات: ") { FontWeight = FontWeights.Bold });
                notesSection.Inlines.Add(new System.Windows.Documents.Run(sale.Notes));
                document.Blocks.Add(notesSection);
            }

            // التوقيعات
            document.Blocks.Add(new System.Windows.Documents.Paragraph() { Margin = new Thickness(0, 40, 0, 20) });

            var signaturesTable = new System.Windows.Documents.Table()
            {
                CellSpacing = 0
            };
            signaturesTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            signaturesTable.Columns.Add(new System.Windows.Documents.TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var signaturesRowGroup = new System.Windows.Documents.TableRowGroup();
            var signaturesRow = new System.Windows.Documents.TableRow();

            var sellerSignature = new System.Windows.Documents.TableCell()
            {
                Padding = new Thickness(20)
            };
            var sellerPara = new System.Windows.Documents.Paragraph()
            {
                TextAlignment = TextAlignment.Center
            };
            sellerPara.Inlines.Add(new System.Windows.Documents.Run("توقيع البائع") { FontWeight = FontWeights.Bold });
            sellerPara.Inlines.Add(new System.Windows.Documents.LineBreak());
            sellerPara.Inlines.Add(new System.Windows.Documents.LineBreak());
            sellerPara.Inlines.Add(new System.Windows.Documents.Run("_________________"));
            sellerSignature.Blocks.Add(sellerPara);

            var customerSignature = new System.Windows.Documents.TableCell()
            {
                Padding = new Thickness(20)
            };
            var customerPara = new System.Windows.Documents.Paragraph()
            {
                TextAlignment = TextAlignment.Center
            };
            customerPara.Inlines.Add(new System.Windows.Documents.Run("توقيع العميل") { FontWeight = FontWeights.Bold });
            customerPara.Inlines.Add(new System.Windows.Documents.LineBreak());
            customerPara.Inlines.Add(new System.Windows.Documents.LineBreak());
            customerPara.Inlines.Add(new System.Windows.Documents.Run("_________________"));
            customerSignature.Blocks.Add(customerPara);

            signaturesRow.Cells.Add(sellerSignature);
            signaturesRow.Cells.Add(customerSignature);
            signaturesRowGroup.Rows.Add(signaturesRow);
            signaturesTable.RowGroups.Add(signaturesRowGroup);
            document.Blocks.Add(signaturesTable);

            // تذييل الفاتورة
            var footer = new System.Windows.Documents.Paragraph()
            {
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 30, 0, 0),
                FontSize = 10,
                Foreground = System.Windows.Media.Brushes.Gray
            };
            footer.Inlines.Add(new System.Windows.Documents.Run("شكراً لتعاملكم معنا | نتمنى لكم دوام الصحة والعافية"));
            footer.Inlines.Add(new System.Windows.Documents.LineBreak());
            footer.Inlines.Add(new System.Windows.Documents.Run($"تم إنشاء هذه الفاتورة في: {DateTime.Now:dd/MM/yyyy HH:mm}"));
            document.Blocks.Add(footer);

            return document;
        }

        private void AddInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditInventoryWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowInventoryAsync(); // تحديث القائمة
            }
        }

        private void AddSaleButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditSaleWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowSalesAsync(); // تحديث القائمة
            }
        }

        private void EditSaleButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sale = button?.DataContext as Sale;

            if (sale != null)
            {
                var editWindow = new AddEditSaleWindow(sale);
                if (editWindow.ShowDialog() == true)
                {
                    ShowSalesAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteSaleButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sale = button?.DataContext as Sale;

            if (sale != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المبيعة '{sale.InvoiceNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.Sales.Remove(sale);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف المبيعة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowSalesAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المبيعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void EditInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as InventoryItem;

            if (item != null)
            {
                var editWindow = new AddEditInventoryWindow(item);
                if (editWindow.ShowDialog() == true)
                {
                    ShowInventoryAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as InventoryItem;

            if (item != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العنصر '{item.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.InventoryItems.Remove(item);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف العنصر بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowInventoryAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void EditDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var device = button?.DataContext as MedicalDevice;

            if (device != null)
            {
                var editWindow = new AddEditDeviceWindow(device);
                if (editWindow.ShowDialog() == true)
                {
                    ShowDevicesAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var device = button?.DataContext as MedicalDevice;

            if (device != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الجهاز '{device.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // التحقق من وجود علاقات مرتبطة
                        var relatedSales = await App.DatabaseContext.Sales
                            .Where(s => s.MedicalDeviceId == device.Id)
                            .CountAsync();

                        var relatedInstallations = await App.DatabaseContext.DeviceInstallations
                            .Where(i => i.DeviceId == device.Id)
                            .CountAsync();

                        if (relatedSales > 0 || relatedInstallations > 0)
                        {
                            MessageBox.Show(
                                $"لا يمكن حذف هذا الجهاز لأنه مرتبط بـ:\n" +
                                $"- {relatedSales} مبيعة\n" +
                                $"- {relatedInstallations} تنصيب\n\n" +
                                "يرجى حذف هذه العلاقات أولاً أو تغيير حالة الجهاز إلى 'غير نشط'.",
                                "لا يمكن الحذف", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        // حذف الأرقام التسلسلية المرتبطة
                        var serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                            .Where(s => s.DeviceId == device.Id)
                            .ToListAsync();

                        if (serialNumbers.Any())
                        {
                            App.DatabaseContext.DeviceSerialNumbers.RemoveRange(serialNumbers);
                        }

                        // حذف الجهاز من المخزون أيضاً
                        var inventoryItem = await App.DatabaseContext.InventoryItems
                            .FirstOrDefaultAsync(i => i.Name == device.Name);

                        if (inventoryItem != null)
                        {
                            App.DatabaseContext.InventoryItems.Remove(inventoryItem);
                        }

                        // حذف الجهاز
                        App.DatabaseContext.MedicalDevices.Remove(device);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف الجهاز بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowDevicesAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الجهاز: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                            "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void ViewDeviceDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var device = button?.DataContext as MedicalDevice;

            if (device != null)
            {
                var detailsWindow = new DeviceDetailsWindow(device);
                detailsWindow.ShowDialog();
            }
        }



        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditCustomerWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowCustomersAsync(); // تحديث القائمة
            }
        }

        private void EditCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var customer = button?.DataContext as Customer;

            if (customer != null)
            {
                var editWindow = new AddEditCustomerWindow(customer);
                if (editWindow.ShowDialog() == true)
                {
                    ShowCustomersAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var customer = button?.DataContext as Customer;

            if (customer != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العميل '{customer.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // التحقق من وجود علاقات مرتبطة
                        var relatedSales = await App.DatabaseContext.Sales
                            .Where(s => s.CustomerId == customer.Id)
                            .CountAsync();

                        var relatedInstallations = await App.DatabaseContext.DeviceInstallations
                            .Where(i => i.CustomerId == customer.Id)
                            .CountAsync();

                        if (relatedSales > 0 || relatedInstallations > 0)
                        {
                            MessageBox.Show(
                                $"لا يمكن حذف هذا العميل لأنه مرتبط بـ:\n" +
                                $"- {relatedSales} مبيعة\n" +
                                $"- {relatedInstallations} تنصيب\n\n" +
                                "يرجى حذف هذه العلاقات أولاً أو تغيير حالة العميل إلى 'غير نشط'.",
                                "لا يمكن الحذف", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        App.DatabaseContext.Customers.Remove(customer);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف العميل بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowCustomersAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                            "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void AddSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditSupplierWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowSuppliersAsync(); // تحديث القائمة
            }
        }

        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var supplier = button?.DataContext as Supplier;

            if (supplier != null)
            {
                var editWindow = new AddEditSupplierWindow(supplier);
                if (editWindow.ShowDialog() == true)
                {
                    ShowSuppliersAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var supplier = button?.DataContext as Supplier;

            if (supplier != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{supplier.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.Suppliers.Remove(supplier);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف المورد بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowSuppliersAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void AddShipmentButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditShipmentWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowShipmentsAsync(); // تحديث القائمة
            }
        }

        private void EditShipmentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var shipment = button?.DataContext as Shipment;

            if (shipment != null)
            {
                var editWindow = new AddEditShipmentWindow(shipment);
                if (editWindow.ShowDialog() == true)
                {
                    ShowShipmentsAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteShipmentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var shipment = button?.DataContext as Shipment;

            if (shipment != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الشحنة '{shipment.ShipmentNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.Shipments.Remove(shipment);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف الشحنة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowShipmentsAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الشحنة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void AddMaintenanceButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditMaintenanceWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowMaintenanceAsync(); // تحديث القائمة
            }
        }

        private void EditMaintenanceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var maintenance = button?.DataContext as MaintenanceRecord;

            if (maintenance != null)
            {
                var editWindow = new AddEditMaintenanceWindow(maintenance);
                if (editWindow.ShowDialog() == true)
                {
                    ShowMaintenanceAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteMaintenanceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var maintenance = button?.DataContext as MaintenanceRecord;

            if (maintenance != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف سجل الصيانة للجهاز '{maintenance.DeviceName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.MaintenanceRecords.Remove(maintenance);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف سجل الصيانة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowMaintenanceAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف سجل الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void AddInstallationButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditInstallationWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowInstallationsAsync(); // تحديث القائمة
            }
        }

        private void EditInstallationButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var installation = button?.DataContext as DeviceInstallation;

            if (installation != null)
            {
                var editWindow = new AddEditInstallationWindow(installation);
                if (editWindow.ShowDialog() == true)
                {
                    ShowInstallationsAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteInstallationButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var installation = button?.DataContext as DeviceInstallation;

            if (installation != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف تنصيب الجهاز '{installation.DeviceName}' للعميل '{installation.CustomerName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.DeviceInstallations.Remove(installation);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف التنصيب بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowInstallationsAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف التنصيب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            App.DatabaseContext?.Dispose();
            base.OnClosed(e);
        }

        // دوال البحث والفلترة
        private void FilterDevices(string searchText, DataGrid dataGrid, System.Collections.Generic.List<MedicalDevice> allDevices)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allDevices;
                return;
            }

            var filteredDevices = allDevices.Where(d =>
                d.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                d.Brand.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                d.Model.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                d.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                d.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredDevices;
        }

        private void FilterInventory(string searchText, DataGrid dataGrid, System.Collections.Generic.List<InventoryItem> allItems)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allItems;
                return;
            }

            var filteredItems = allItems.Where(i =>
                i.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                i.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                i.Unit.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                i.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredItems;
        }

        private void FilterSales(string searchText, DataGrid dataGrid, System.Collections.Generic.List<Sale> allSales)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allSales;
                return;
            }

            var filteredSales = allSales.Where(s =>
                s.InvoiceNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.CustomerName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.DeviceName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.PaymentStatus.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredSales;
        }

        private void FilterCustomers(string searchText, DataGrid dataGrid, System.Collections.Generic.List<Customer> allCustomers)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allCustomers;
                return;
            }

            var filteredCustomers = allCustomers.Where(c =>
                c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.CustomerType.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.City.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredCustomers;
        }

        private void FilterSuppliers(string searchText, DataGrid dataGrid, System.Collections.Generic.List<Supplier> allSuppliers)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allSuppliers;
                return;
            }

            var filteredSuppliers = allSuppliers.Where(s =>
                s.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.City.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredSuppliers;
        }

        private void FilterShipments(string searchText, DataGrid dataGrid, System.Collections.Generic.List<Shipment> allShipments)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allShipments;
                return;
            }

            var filteredShipments = allShipments.Where(s =>
                s.ShipmentNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.RecipientName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.DeliveryAddress.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                s.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (s.TrackingNumber != null && s.TrackingNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            ).ToList();

            dataGrid.ItemsSource = filteredShipments;
        }

        private void FilterMaintenance(string searchText, DataGrid dataGrid, System.Collections.Generic.List<MaintenanceRecord> allMaintenance)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                dataGrid.ItemsSource = allMaintenance;
                return;
            }

            var filteredMaintenance = allMaintenance.Where(m =>
                m.DeviceName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                m.MaintenanceType.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                m.Status.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                m.TechnicianName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                m.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            dataGrid.ItemsSource = filteredMaintenance;
        }

        // دوال حساب الإحصائيات الحقيقية
        private async System.Threading.Tasks.Task<(string TotalSales, string SalesChange)> CalculateSalesStatsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var lastMonth = thisMonth.AddMonths(-1);

                var thisMonthSales = await App.DatabaseContext.Sales
                    .Where(s => s.SaleDate >= thisMonth)
                    .SumAsync(s => s.FinalAmount);

                var lastMonthSales = await App.DatabaseContext.Sales
                    .Where(s => s.SaleDate >= lastMonth && s.SaleDate < thisMonth)
                    .SumAsync(s => s.FinalAmount);

                var changePercent = lastMonthSales > 0 ? ((thisMonthSales - lastMonthSales) / lastMonthSales) * 100 : 0;
                var changeText = changePercent >= 0 ? $"+{changePercent:F1}%" : $"{changePercent:F1}%";

                return ($"{thisMonthSales:C}", changeText);
            }
            catch
            {
                return ("0 ر.س", "0%");
            }
        }

        private async System.Threading.Tasks.Task<(int AvailableItems, int LowStockItems)> CalculateInventoryStatsAsync()
        {
            try
            {
                var availableItems = await App.DatabaseContext.InventoryItems
                    .Where(i => i.Status == "متاح")
                    .CountAsync();

                var lowStockItems = await App.DatabaseContext.InventoryItems
                    .Where(i => i.CurrentStock <= i.MinimumStock)
                    .CountAsync();

                return (availableItems, lowStockItems);
            }
            catch
            {
                return (0, 0);
            }
        }

        private async System.Threading.Tasks.Task<(int ActiveCustomers, int NewCustomers)> CalculateCustomerStatsAsync()
        {
            try
            {
                var activeCustomers = await App.DatabaseContext.Customers
                    .Where(c => c.Status == "نشط")
                    .CountAsync();

                var thisMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var newCustomers = await App.DatabaseContext.Customers
                    .Where(c => c.CreatedDate >= thisMonth)
                    .CountAsync();

                return (activeCustomers, newCustomers);
            }
            catch
            {
                return (0, 0);
            }
        }

        private async System.Threading.Tasks.Task<(int PendingMaintenance, int OverdueMaintenance)> CalculateMaintenanceStatsAsync()
        {
            try
            {
                var pendingMaintenance = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => m.Status == "مجدولة" || m.Status == "قيد التنفيذ")
                    .CountAsync();

                var overdueMaintenance = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => m.Status == "مجدولة" && m.MaintenanceDate < DateTime.Today)
                    .CountAsync();

                return (pendingMaintenance, overdueMaintenance);
            }
            catch
            {
                return (0, 0);
            }
        }

        private async System.Threading.Tasks.Task<(int InTransitShipments, int DeliveredToday)> CalculateShipmentStatsAsync()
        {
            try
            {
                var inTransitShipments = await App.DatabaseContext.Shipments
                    .Where(s => s.Status == "في الطريق" || s.Status == "جاهزة للشحن")
                    .CountAsync();

                var deliveredToday = await App.DatabaseContext.Shipments
                    .Where(s => s.Status == "تم التسليم" && s.ShipmentDate.Date == DateTime.Today)
                    .CountAsync();

                return (inTransitShipments, deliveredToday);
            }
            catch
            {
                return (0, 0);
            }
        }

        private async System.Threading.Tasks.Task<(string NetProfit, string ProfitMargin)> CalculateProfitStatsAsync()
        {
            try
            {
                var thisMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);

                var totalRevenue = await App.DatabaseContext.Sales
                    .Where(s => s.SaleDate >= thisMonth)
                    .SumAsync(s => s.FinalAmount);

                var totalMaintenanceCost = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => m.MaintenanceDate >= thisMonth)
                    .SumAsync(m => m.Cost);

                var netProfit = totalRevenue - totalMaintenanceCost;
                var profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

                return ($"{netProfit:C}", $"{profitMargin:F1}%");
            }
            catch
            {
                return ("0 ر.س", "0%");
            }
        }

        // دالة إنشاء البطاقات التفاعلية
        private Border CreateInteractiveStatsCard(string icon, string title, string mainValue, string subValue, string color, System.Action clickAction)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                BorderThickness = new Thickness(0, 0, 0, 4),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(10),
                Padding = new Thickness(20),
                Cursor = System.Windows.Input.Cursors.Hand,
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 3,
                    Opacity = 0.3
                }
            };

            var stackPanel = new StackPanel();

            // الأيقونة والعنوان
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 15)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 24,
                Margin = new Thickness(0, 0, 10, 0)
            };

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87)),
                VerticalAlignment = VerticalAlignment.Center
            };

            headerPanel.Children.Add(iconText);
            headerPanel.Children.Add(titleText);
            stackPanel.Children.Add(headerPanel);

            // القيمة الرئيسية
            var mainValueText = new TextBlock
            {
                Text = mainValue,
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Margin = new Thickness(0, 0, 0, 5)
            };
            stackPanel.Children.Add(mainValueText);

            // القيمة الفرعية
            var subValueText = new TextBlock
            {
                Text = subValue,
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            stackPanel.Children.Add(subValueText);

            card.Child = stackPanel;

            // إضافة الحدث
            card.MouseLeftButtonUp += (s, e) => clickAction?.Invoke();

            // تأثيرات التفاعل
            card.MouseEnter += (s, e) =>
            {
                card.Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));
                card.Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 5,
                    Opacity = 0.5
                };
            };

            card.MouseLeave += (s, e) =>
            {
                card.Background = Brushes.White;
                card.Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 3,
                    Opacity = 0.3
                };
            };

            return card;
        }

        // دالة إنشاء أزرار الاختصارات
        private Button CreateShortcutButton(string icon, string text, string color, System.Action clickAction)
        {
            var button = new Button
            {
                Height = 80,
                Margin = new Thickness(5),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = System.Windows.Input.Cursors.Hand,
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 2,
                    Opacity = 0.3
                }
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 20,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var buttonText = new TextBlock
            {
                Text = text,
                FontSize = 12,
                FontWeight = FontWeights.SemiBold,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            stackPanel.Children.Add(iconText);
            stackPanel.Children.Add(buttonText);
            button.Content = stackPanel;

            button.Click += (s, e) => clickAction?.Invoke();

            return button;
        }

        // دالة الحصول على الأنشطة الأخيرة
        private async System.Threading.Tasks.Task<System.Collections.Generic.List<(string Icon, string Activity, string Time)>> GetRecentActivitiesAsync()
        {
            var activities = new System.Collections.Generic.List<(string Icon, string Activity, string Time)>();

            try
            {
                // آخر المبيعات
                var recentSales = await App.DatabaseContext.Sales
                    .OrderByDescending(s => s.SaleDate)
                    .Take(3)
                    .ToListAsync();

                foreach (var sale in recentSales)
                {
                    activities.Add(("💰", $"مبيعة جديدة للعميل {sale.CustomerName} - {sale.FinalAmount:C}", GetTimeAgo(sale.SaleDate)));
                }

                // آخر العملاء
                var recentCustomers = await App.DatabaseContext.Customers
                    .OrderByDescending(c => c.CreatedDate)
                    .Take(2)
                    .ToListAsync();

                foreach (var customer in recentCustomers)
                {
                    activities.Add(("👥", $"عميل جديد: {customer.Name} - {customer.CustomerType}", GetTimeAgo(customer.CreatedDate)));
                }

                // آخر أعمال الصيانة
                var recentMaintenance = await App.DatabaseContext.MaintenanceRecords
                    .OrderByDescending(m => m.LastUpdated)
                    .Take(2)
                    .ToListAsync();

                foreach (var maintenance in recentMaintenance)
                {
                    activities.Add(("🛠️", $"صيانة {maintenance.MaintenanceType} للجهاز {maintenance.DeviceName}", GetTimeAgo(maintenance.LastUpdated)));
                }
            }
            catch
            {
                activities.Add(("ℹ️", "لا توجد أنشطة حديثة", "الآن"));
            }

            return activities.OrderByDescending(a => a.Time).Take(7).ToList();
        }

        // دالة إنشاء لوحة الأنشطة الأخيرة
        private Border CreateRecentActivitiesPanel(System.Collections.Generic.List<(string Icon, string Activity, string Time)> activities)
        {
            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 2,
                    Opacity = 0.2
                }
            };

            var stackPanel = new StackPanel();

            foreach (var activity in activities)
            {
                var activityPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 0, 0, 10)
                };

                var iconText = new TextBlock
                {
                    Text = activity.Icon,
                    FontSize = 16,
                    Margin = new Thickness(0, 0, 10, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                var activityText = new TextBlock
                {
                    Text = activity.Activity,
                    FontSize = 14,
                    VerticalAlignment = VerticalAlignment.Center,
                    TextWrapping = TextWrapping.Wrap,
                    Width = 400
                };

                var timeText = new TextBlock
                {
                    Text = activity.Time,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    VerticalAlignment = VerticalAlignment.Center,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(10, 0, 0, 0)
                };

                activityPanel.Children.Add(iconText);
                activityPanel.Children.Add(activityText);
                activityPanel.Children.Add(timeText);

                stackPanel.Children.Add(activityPanel);

                // خط فاصل
                if (activity != activities.Last())
                {
                    var separator = new Separator
                    {
                        Margin = new Thickness(0, 5, 0, 5),
                        Background = new SolidColorBrush(Color.FromRgb(233, 236, 239))
                    };
                    stackPanel.Children.Add(separator);
                }
            }

            border.Child = stackPanel;
            return border;
        }

        // دالة حساب الوقت المنقضي
        private string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "الآن";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} دقيقة";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} ساعة";
            else if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} يوم";
            else
                return dateTime.ToString("dd/MM/yyyy");
        }

        private void ViewInstallationReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is DeviceInstallation installation)
            {
                try
                {
                    if (!string.IsNullOrEmpty(installation.InstallationReportBookletPath) &&
                        System.IO.File.Exists(installation.InstallationReportBookletPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = installation.InstallationReportBookletPath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("كتيب التقرير غير موجود أو تم حذفه.", "تنبيه",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح كتيب التقرير: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    // محول لعرض اسم الملف فقط
    public class FileNameConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string filePath && !string.IsNullOrEmpty(filePath))
            {
                return Path.GetFileName(filePath);
            }
            return "لا يوجد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // دوال إدارة قطع الغيار والملحقات
    public partial class MainWindow
    {
        private async void ShowSparePartsAsync()
        {
            var content = new StackPanel();

            var title = new TextBlock
            {
                Text = "🔧 قطع الغيار والملحقات",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            content.Children.Add(title);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var addButton = new Button
            {
                Content = "➕ إضافة قطعة غيار",
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 10, 0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            addButton.Click += AddSparePartButton_Click;

            var refreshButton = new Button
            {
                Content = "🔄 تحديث",
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(0, 0, 10, 0),
                Padding = new Thickness(15, 8, 15, 8),
                FontWeight = FontWeights.SemiBold
            };
            refreshButton.Click += (s, e) => ShowSparePartsAsync();

            actionsPanel.Children.Add(addButton);
            actionsPanel.Children.Add(refreshButton);
            content.Children.Add(actionsPanel);

            // الجدول المتقدم لقطع الغيار
            var advancedGrid = new Controls.AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableKeyboardShortcuts = true,
                EnableMultiSelect = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                EnableAutoFitColumns = true,
                Height = 600
            };

            // إعداد الأعمدة
            var columns = new List<DataGridColumn>
            {
                new DataGridTextColumn { Header = "اسم القطعة", Binding = new System.Windows.Data.Binding("Name"), Width = 150 },
                new DataGridTextColumn { Header = "الجهاز المرتبط", Binding = new System.Windows.Data.Binding("AssociatedDeviceName"), Width = 150 },
                new DataGridTextColumn { Header = "العدد المتوفر", Binding = new System.Windows.Data.Binding("AvailableQuantity"), Width = 80 },
                new DataGridTextColumn { Header = "سعر الشراء", Binding = new System.Windows.Data.Binding("PurchasePrice") { StringFormat = "{0:C}" }, Width = 90 },
                new DataGridTextColumn { Header = "سعر البيع مفرد", Binding = new System.Windows.Data.Binding("IndividualSellingPrice") { StringFormat = "{0:C}" }, Width = 100 },
                new DataGridTextColumn { Header = "سعر البيع جملة", Binding = new System.Windows.Data.Binding("WholesaleSellingPrice") { StringFormat = "{0:C}" }, Width = 100 },
                new DataGridTextColumn { Header = "تاريخ الشراء", Binding = new System.Windows.Data.Binding("PurchaseDate") { StringFormat = "{0:dd/MM/yyyy}" }, Width = 90 },
                new DataGridTextColumn { Header = "موقع التخزين", Binding = new System.Windows.Data.Binding("WorkshopStorageLocation"), Width = 120 }
            };

            advancedGrid.SetColumns(columns);

            // إعداد معالجات الأحداث للجدول المتقدم
            advancedGrid.EditRequested += (sender, sparePart) =>
            {
                var editWindow = new Windows.AddEditSparePartWindow(sparePart as SparePart);
                if (editWindow.ShowDialog() == true)
                {
                    ShowSparePartsAsync(); // تحديث القائمة
                }
            };

            advancedGrid.DeleteRequested += async (sender, sparePart) =>
            {
                var sparePartObj = sparePart as SparePart;
                if (sparePartObj != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف قطعة الغيار '{sparePartObj.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nملاحظة: يمكنك استخدام P للحذف السريع",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            App.DatabaseContext.SpareParts.Remove(sparePartObj);
                            await App.DatabaseContext.SaveChangesAsync();

                            MessageBox.Show("تم حذف قطعة الغيار بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            ShowSparePartsAsync(); // تحديث القائمة
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف قطعة الغيار: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                                "خطأ في الحذف", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            };

            advancedGrid.ViewRequested += (sender, sparePart) =>
            {
                var sparePartObj = sparePart as SparePart;
                if (sparePartObj != null)
                {
                    MessageBox.Show($"تفاصيل قطعة الغيار:\n\n" +
                                   $"اسم القطعة: {sparePartObj.Name}\n" +
                                   $"الجهاز المرتبط: {sparePartObj.AssociatedDeviceName}\n" +
                                   $"العدد المتوفر: {sparePartObj.AvailableQuantity}\n" +
                                   $"سعر الشراء: {sparePartObj.PurchasePrice:C}\n" +
                                   $"سعر البيع مفرد: {sparePartObj.IndividualSellingPrice:C}\n" +
                                   $"سعر البيع جملة: {sparePartObj.WholesaleSellingPrice:C}\n" +
                                   $"تاريخ الشراء: {sparePartObj.PurchaseDate:dd/MM/yyyy}\n" +
                                   $"موقع التخزين: {sparePartObj.WorkshopStorageLocation}",
                                   "تفاصيل قطعة الغيار", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            };

            advancedGrid.DataRefreshRequested += (sender, e) =>
            {
                ShowSparePartsAsync();
            };

            try
            {
                var spareParts = await App.DatabaseContext.SpareParts.ToListAsync();
                advancedGrid.SetDataSource(spareParts);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قطع الغيار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            content.Children.Add(advancedGrid);
            ContentArea.Content = content;
        }

        private void AddSparePartButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new Windows.AddEditSparePartWindow();
            if (addWindow.ShowDialog() == true)
            {
                ShowSparePartsAsync(); // تحديث القائمة
            }
        }

        private void EditSparePartButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sparePart = button?.DataContext as SparePart;

            if (sparePart != null)
            {
                var editWindow = new Windows.AddEditSparePartWindow(sparePart);
                if (editWindow.ShowDialog() == true)
                {
                    ShowSparePartsAsync(); // تحديث القائمة
                }
            }
        }

        private async void DeleteSparePartButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sparePart = button?.DataContext as SparePart;

            if (sparePart != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف قطعة الغيار '{sparePart.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.SpareParts.Remove(sparePart);
                        await App.DatabaseContext.SaveChangesAsync();

                        MessageBox.Show("تم حذف قطعة الغيار بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        ShowSparePartsAsync(); // تحديث القائمة
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف قطعة الغيار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
