using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditMaintenanceWindow : Window
    {
        private MaintenanceRecord _maintenance;
        private bool _isEditMode;
        private MedicalDevice _selectedDevice;
        private string _documentsPath = "";
        
        public AddEditMaintenanceWindow(MaintenanceRecord maintenance = null)
        {
            try
            {
                InitializeComponent();
                _maintenance = maintenance;
                _isEditMode = maintenance != null;

                // تعيين القيم الافتراضية الآمنة أولاً
                MaintenanceDatePicker.SelectedDate = DateTime.Now;
                CostTextBox.Text = "0";
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");

                if (_isEditMode)
                {
                    TitleBlock.Text = "تعديل سجل صيانة";
                }

                // تحميل البيانات
                LoadDataAsync();
                LoadTechniciansAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الصيانة: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                // إغلاق النافذة في حالة الخطأ
                this.Close();
            }
        }
        
        private async void LoadDataAsync()
        {
            try
            {
                if (!_isEditMode)
                {
                    // تعيين القيم الافتراضية للـ ComboBox في وضع الإضافة
                    if (MaintenanceTypeComboBox.Items.Count > 0)
                        MaintenanceTypeComboBox.SelectedIndex = 0;

                    if (StatusComboBox.Items.Count > 0)
                        StatusComboBox.SelectedIndex = 0;

                    if (TechnicianComboBox.Items.Count > 0)
                        TechnicianComboBox.SelectedIndex = 0;
                }
                else if (_isEditMode)
                {
                    // تحميل بيانات الصيانة في وضع التعديل
                    LoadMaintenanceData();
                    // تحميل معلومات الجهاز المختار
                    await LoadSelectedDeviceAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadMaintenanceData()
        {
            try
            {
                if (_maintenance == null) return;

                MaintenanceDatePicker.SelectedDate = _maintenance.MaintenanceDate;
                CostTextBox.Text = _maintenance.Cost.ToString();
                DescriptionTextBox.Text = _maintenance.Description ?? "";
                NotesTextBox.Text = _maintenance.Notes ?? "";
                SupplierCompanyTextBox.Text = _maintenance.SupplierCompany ?? "";
                CreatedDateTextBlock.Text = _maintenance.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = _maintenance.LastUpdated.ToString("dd/MM/yyyy HH:mm");

            // تحميل كتيب التقرير
            if (!string.IsNullOrEmpty(_maintenance.ReportBookletPath))
            {
                ReportBookletPathTextBox.Text = Path.GetFileName(_maintenance.ReportBookletPath);
                ViewReportBtn.IsEnabled = File.Exists(_maintenance.ReportBookletPath);
                RemoveReportBtn.IsEnabled = true;
            }

            // تحميل الأوراق والمخاطبات
            if (!string.IsNullOrEmpty(_maintenance.DocumentsPath))
            {
                _documentsPath = _maintenance.DocumentsPath;
                DocumentsTextBox.Text = Path.GetFileName(_maintenance.DocumentsPath);
                RemoveDocumentBtn.IsEnabled = true;
            }

            // تحميل الجهاز المرتبط بسجل الصيانة
            _ = LoadSelectedDeviceAsync();

            // تعيين نوع الصيانة
            foreach (ComboBoxItem item in MaintenanceTypeComboBox.Items)
            {
                if (item.Content.ToString() == _maintenance.MaintenanceType)
                {
                    MaintenanceTypeComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == _maintenance.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الفني
            foreach (var item in TechnicianComboBox.Items)
            {
                if (item.ToString() == _maintenance.TechnicianName)
                {
                    TechnicianComboBox.SelectedItem = item;
                    break;
                }
            }

                // تحميل تاريخ الصيانة القادم إذا كانت الحالة مجدولة
                if (_maintenance.NextMaintenanceDate.HasValue)
                {
                    NextMaintenanceDatePicker.SelectedDate = _maintenance.NextMaintenanceDate;
                    NextMaintenanceDatePanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSelectedDeviceAsync()
        {
            try
            {
                if (_maintenance?.MedicalDeviceId > 0)
                {
                    _selectedDevice = await App.DatabaseContext.MedicalDevices
                        .FirstOrDefaultAsync(d => d.Id == _maintenance.MedicalDeviceId);

                    if (_selectedDevice != null)
                    {
                        // عرض معلومات الجهاز في الحقل
                        SelectedDeviceTextBox.Text = $"{_selectedDevice.Name} - {_selectedDevice.SerialNumber}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectDeviceWindow = new SelectDeviceWindow();
                selectDeviceWindow.Owner = this;

                if (selectDeviceWindow.ShowDialog() == true && selectDeviceWindow.SelectedDevice != null)
                {
                    _selectedDevice = selectDeviceWindow.SelectedDevice;
                    SelectedDeviceTextBox.Text = $"{_selectedDevice.Name} - {_selectedDevice.SerialNumber}";

                    // تحديث معلومات الجهاز
                    DeviceInfoTextBlock.Text = $"الجهاز: {_selectedDevice.Name} - الماركة: {_selectedDevice.Brand} - الموديل: {_selectedDevice.Model} - الحالة: {_selectedDevice.Status}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // تم إزالة دالة LoadSerialNumbersAsync - لم تعد مطلوبة
        // الرقم التسلسلي أصبح حقل نص حر

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (StatusComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                // إظهار حقل تاريخ الصيانة القادم فقط عند اختيار "مجدولة"
                if (selectedItem.Content.ToString() == "مجدولة")
                {
                    NextMaintenanceDatePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    NextMaintenanceDatePanel.Visibility = Visibility.Collapsed;
                    NextMaintenanceDatePicker.SelectedDate = null; // مسح التاريخ عند إخفاء الحقل
                }
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateMaintenanceData();
                    App.DatabaseContext.MaintenanceRecords.Update(_maintenance);
                }
                else
                {
                    _maintenance = new MaintenanceRecord();
                    UpdateMaintenanceData();
                    _maintenance.CreatedDate = DateTime.Now;
                    App.DatabaseContext.MaintenanceRecords.Add(_maintenance);
                }
                
                _maintenance.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث سجل الصيانة بنجاح!" : "تم إضافة سجل الصيانة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateMaintenanceData()
        {
            if (_selectedDevice == null)
            {
                throw new InvalidOperationException("لم يتم اختيار جهاز");
            }

            _maintenance.MedicalDeviceId = _selectedDevice.Id;
            _maintenance.DeviceName = _selectedDevice.Name;
            _maintenance.SerialNumber = _selectedDevice.SerialNumber;
            _maintenance.MaintenanceType = ((ComboBoxItem)MaintenanceTypeComboBox.SelectedItem).Content.ToString();
            _maintenance.MaintenanceDate = MaintenanceDatePicker.SelectedDate ?? DateTime.Now;
            _maintenance.Status = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
            _maintenance.Cost = decimal.Parse(CostTextBox.Text);
            _maintenance.TechnicianName = TechnicianComboBox.SelectedItem?.ToString() ?? "";
            _maintenance.Description = DescriptionTextBox.Text.Trim();
            _maintenance.Notes = NotesTextBox.Text.Trim();
            _maintenance.DocumentsPath = _documentsPath;
            _maintenance.SupplierCompany = SupplierCompanyTextBox.Text.Trim();

            // حفظ تاريخ الصيانة القادم إذا كانت الحالة مجدولة
            if (_maintenance.Status == "مجدولة" && NextMaintenanceDatePicker.SelectedDate.HasValue)
            {
                _maintenance.NextMaintenanceDate = NextMaintenanceDatePicker.SelectedDate;
            }
            else
            {
                _maintenance.NextMaintenanceDate = null;
            }

            // حفظ مسار كتيب التقرير
            if (ReportBookletPathTextBox.Text != "لم يتم اختيار ملف")
            {
                _maintenance.ReportBookletPath = ReportBookletPathTextBox.Tag?.ToString() ?? "";
            }
        }
        
        private bool ValidateInput()
        {
            if (_selectedDevice == null)
            {
                MessageBox.Show("يرجى اختيار الجهاز الطبي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (MaintenanceTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (MaintenanceDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(CostTextBox.Text, out decimal cost) || cost < 0)
            {
                MessageBox.Show("يرجى إدخال تكلفة صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (TechnicianComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفني", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BrowseReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "اختيار كتيب تقرير الصيانة",
                    Filter = "PDF Files|*.pdf|Word Documents|*.doc;*.docx|Image Files|*.jpg;*.jpeg;*.png;*.bmp|All Files|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == true)
                {
                    var fileName = openDialog.FileName;
                    var fileInfo = new FileInfo(fileName);

                    // التحقق من حجم الملف (أقل من 10 ميجابايت)
                    if (fileInfo.Length > 10 * 1024 * 1024)
                    {
                        MessageBox.Show("حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.",
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // نسخ الملف إلى مجلد التطبيق
                    var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                                  "MedicalDevicesManager", "MaintenanceReports");
                    Directory.CreateDirectory(appDataPath);

                    var newFileName = $"Maintenance_{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(fileName)}";
                    var destinationPath = Path.Combine(appDataPath, newFileName);

                    File.Copy(fileName, destinationPath, true);

                    ReportBookletPathTextBox.Text = Path.GetFileName(destinationPath);
                    ReportBookletPathTextBox.Tag = destinationPath;
                    ViewReportBtn.IsEnabled = true;
                    RemoveReportBtn.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = ReportBookletPathTextBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ResetReportBooklet();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveReportBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف كتيب التقرير؟", "تأكيد الحذف",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var filePath = ReportBookletPathTextBox.Tag?.ToString();
                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch (Exception ex)
                {
                    // تجاهل أخطاء حذف الملف
                    System.Diagnostics.Debug.WriteLine($"Error deleting file: {ex.Message}");
                }

                ResetReportBooklet();
            }
        }

        private void ResetReportBooklet()
        {
            ReportBookletPathTextBox.Text = "لم يتم اختيار ملف";
            ReportBookletPathTextBox.Tag = null;
            ViewReportBtn.IsEnabled = false;
            RemoveReportBtn.IsEnabled = false;
        }

        private async void LoadTechniciansAsync()
        {
            try
            {
                var technicians = await App.DatabaseContext.TechnicianNames
                    .Where(t => t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                TechnicianComboBox.ItemsSource = technicians;

                // إضافة فنيين افتراضيين إذا لم يوجدوا
                if (!technicians.Any())
                {
                    var defaultTechnicians = new[]
                    {
                        new TechnicianName { Name = "أحمد محمد", Specialization = "صيانة عامة", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "سعد العلي", Specialization = "أجهزة طبية", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "محمد الأحمد", Specialization = "أجهزة تشخيص", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "خالد السعد", Specialization = "أجهزة جراحية", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "عبدالله الحسن", Specialization = "أجهزة مختبر", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "فني خارجي", Specialization = "متخصص", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now }
                    };

                    App.DatabaseContext.TechnicianNames.AddRange(defaultTechnicians);
                    await App.DatabaseContext.SaveChangesAsync();

                    TechnicianComboBox.ItemsSource = defaultTechnicians;
                    technicians = defaultTechnicians.ToList();
                }

                // تعيين القيمة الافتراضية للفني في وضع الإضافة
                if (!_isEditMode && technicians.Any())
                {
                    TechnicianComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أسماء الفنيين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddDocumentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار ملف الأوراق والمخاطبات",
                    Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.docx)|*.docx|ملفات Excel (*.xlsx)|*.xlsx|الصور (*.jpg;*.png)|*.jpg;*.png",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    _documentsPath = openFileDialog.FileName;
                    DocumentsTextBox.Text = Path.GetFileName(_documentsPath);
                    RemoveDocumentBtn.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveDocumentBtn_Click(object sender, RoutedEventArgs e)
        {
            _documentsPath = "";
            DocumentsTextBox.Text = "لا توجد ملفات مرفقة";
            RemoveDocumentBtn.IsEnabled = false;
        }

        private void ManageTechniciansBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageTechniciansWindow = new ManageTechniciansWindow();
            if (manageTechniciansWindow.ShowDialog() == true)
            {
                LoadTechniciansAsync();
            }
        }
    }
}
