# دليل استخدام الجدول المتقدم (AdvancedDataGrid)

## 📋 نظرة عامة

تم تطوير نظام جداول متقدم ومتكامل يحتوي على جميع المميزات المطلوبة لتحسين تجربة المستخدم وزيادة الإنتاجية في نظام إدارة الأجهزة الطبية.

## 🎯 المميزات الرئيسية

### ✅ **الجدول الموحد المتقدم**
- ترقيم تلقائي للصفوف
- نظام Pagination متقدم مع تحكم كامل
- بحث ذكي فوري في جميع الحقول
- فلترة متقدمة حسب النوع
- تصدير متعدد الصيغ (Excel, PDF, CSV)
- طباعة احترافية
- تحديد متعدد للعمليات المجمعة
- تخصيص شامل للأعمدة

### ✅ **نظام البحث والفلترة**
- بحث عام فوري
- فلاتر سريعة للحالة والتاريخ
- بحث متقدم مع نافذة منفصلة
- البحثات المحفوظة
- عرض الفلاتر النشطة

### ✅ **التصدير والطباعة**
- تصدير Excel مع تنسيق احترافي
- تصدير PDF مع جداول منسقة
- تصدير CSV للاستيراد
- نسخ للحافظة
- طباعة مباشرة مع معاينة

## 🚀 كيفية الاستخدام

### 1. إنشاء جدول متقدم

```csharp
// إنشاء الجدول
var advancedGrid = new AdvancedDataGrid
{
    EnablePagination = true,
    PageSize = 50,
    EnableAdvancedSearch = true,
    EnableExport = true,
    EnablePrint = true,
    EnableRowNumbers = true,
    EnableMultiSelect = true,
    EnableColumnCustomization = true
};
```

### 2. إضافة الأعمدة

```csharp
// أعمدة نصية
advancedGrid.AddTextColumn("الاسم", "Name", 150);
advancedGrid.AddTextColumn("الوصف", "Description", 200);

// أعمدة رقمية
advancedGrid.AddNumericColumn("الكمية", "Quantity", "N0", 100);
advancedGrid.AddCurrencyColumn("السعر", "Price", 120);

// أعمدة التاريخ
advancedGrid.AddDateColumn("تاريخ الإنشاء", "CreatedDate", "yyyy/MM/dd", 120);

// عمود الإجراءات
var actions = new List<ActionButton>
{
    new ActionButton 
    { 
        Icon = "✏️", 
        Tooltip = "تعديل", 
        BackgroundColor = Color.FromRgb(0, 123, 255),
        ClickHandler = EditItem_Click 
    },
    new ActionButton 
    { 
        Icon = "🗑️", 
        Tooltip = "حذف", 
        BackgroundColor = Color.FromRgb(220, 53, 69),
        ClickHandler = DeleteItem_Click 
    }
};
advancedGrid.AddActionsColumn("الإجراءات", actions, 120);
```

### 3. تعيين البيانات

```csharp
// تعيين مصدر البيانات
advancedGrid.SetDataSource(dataList);

// أو تحديث البيانات
advancedGrid.RefreshData();
```

### 4. معالجة الأحداث

```csharp
// النقر المزدوج على الصف
advancedGrid.RowDoubleClick += (sender, item) =>
{
    // فتح نافذة التفاصيل
    ShowItemDetails(item);
};

// تحديد صف واحد
advancedGrid.RowSelected += (sender, item) =>
{
    // عرض التفاصيل في لوحة جانبية
    ShowItemPreview(item);
};

// تحديد متعدد
advancedGrid.MultipleRowsSelected += (sender, items) =>
{
    // تفعيل العمليات المجمعة
    EnableBulkOperations(items);
};

// طلب تحديث البيانات
advancedGrid.DataRefreshRequested += (sender, e) =>
{
    // إعادة تحميل من قاعدة البيانات
    ReloadDataFromDatabase();
};
```

## 🔧 المميزات المتقدمة

### نظام Pagination

```csharp
// تخصيص الـ Pagination
var pagination = new PaginationControl
{
    CurrentPage = 1,
    TotalPages = 10,
    PageSize = 50,
    TotalRecords = 500,
    ShowPageNumbers = true,
    ShowPageSizeSelector = true,
    ShowRecordsInfo = true,
    MaxVisiblePageNumbers = 10
};

// معالجة تغيير الصفحة
pagination.PageChanged += (sender, e) =>
{
    LoadPage(e.NewPage);
};

// معالجة تغيير حجم الصفحة
pagination.PageSizeChanged += (sender, e) =>
{
    UpdatePageSize(e.NewPageSize);
};
```

### نظام البحث المتقدم

```csharp
// إنشاء لوحة البحث
var searchPanel = new AdvancedSearchPanel
{
    EnableGlobalSearch = true,
    EnableQuickFilters = true,
    EnableSavedSearches = true,
    EnableActiveFiltersDisplay = true,
    EnableRealTimeSearch = true
};

// إضافة حقول البحث
searchPanel.AddSearchField(new SearchField
{
    Name = "Name",
    DisplayName = "الاسم",
    Type = SearchFieldType.Text
});

searchPanel.AddSearchField(new SearchField
{
    Name = "Status",
    DisplayName = "الحالة",
    Type = SearchFieldType.Dropdown,
    Options = new List<string> { "نشط", "غير نشط", "معلق" }
});

// معالجة أحداث البحث
searchPanel.SearchRequested += (sender, e) =>
{
    ApplySearch(e.SearchTerm, e.Filters);
};

searchPanel.FilterChanged += (sender, e) =>
{
    ApplyFilter(e.FilterName, e.FilterValue);
};
```

### العمليات السريعة

```csharp
// تصدير سريع
advancedGrid.QuickExportToExcel("تقرير_الأجهزة.xlsx");

// طباعة سريعة
advancedGrid.QuickPrint();

// نسخ المحدد للحافظة
advancedGrid.CopySelectedToClipboard();

// الحصول على إحصائيات
var stats = advancedGrid.GetDataStatistics();
Console.WriteLine($"إجمالي السجلات: {stats.TotalRecords}");
Console.WriteLine($"السجلات المفلترة: {stats.FilteredRecords}");
Console.WriteLine($"السجلات المحددة: {stats.SelectedRecords}");
```

## 📁 هيكل الملفات

```
📁 Controls/
├── AdvancedDataGrid.cs              // الكلاس الرئيسي
├── AdvancedDataGridMethods.cs       // الوظائف الأساسية
├── AdvancedDataGridDialogs.cs       // النوافذ والحوارات
├── PaginationControl.cs             // عنصر التنقل بين الصفحات
├── AdvancedSearchPanel.cs           // لوحة البحث المتقدم
└── SearchDialogs.cs                 // نوافذ البحث

📁 Examples/
└── AdvancedDataGridExample.cs       // مثال شامل للاستخدام
```

## 🎨 التخصيص والتنسيق

### تخصيص الألوان

```csharp
// تخصيص ألوان الأزرار
var editButton = new ActionButton
{
    Icon = "✏️",
    Tooltip = "تعديل",
    BackgroundColor = Color.FromRgb(0, 123, 255), // أزرق
    ClickHandler = Edit_Click
};

var deleteButton = new ActionButton
{
    Icon = "🗑️",
    Tooltip = "حذف",
    BackgroundColor = Color.FromRgb(220, 53, 69), // أحمر
    ClickHandler = Delete_Click
};
```

### تخصيص التنسيق

```csharp
// تنسيق الأعمدة
advancedGrid.AddDateColumn("التاريخ", "Date", "dd/MM/yyyy", 120);
advancedGrid.AddNumericColumn("الرقم", "Number", "N2", 100);
advancedGrid.AddCurrencyColumn("المبلغ", "Amount", 120);
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **البيانات لا تظهر**
   ```csharp
   // تأكد من تعيين البيانات بشكل صحيح
   advancedGrid.SetDataSource(dataList);
   ```

2. **الـ Pagination لا يعمل**
   ```csharp
   // تأكد من تفعيل الـ Pagination
   advancedGrid.EnablePagination = true;
   ```

3. **البحث لا يعمل**
   ```csharp
   // تأكد من تفعيل البحث المتقدم
   advancedGrid.EnableAdvancedSearch = true;
   ```

4. **التصدير يفشل**
   ```csharp
   // تأكد من وجود المراجع المطلوبة
   // EPPlus للـ Excel
   // iText7 للـ PDF
   ```

## 📊 الأداء والتحسين

### نصائح لتحسين الأداء

1. **استخدم Pagination للبيانات الكبيرة**
   ```csharp
   advancedGrid.PageSize = 50; // بدلاً من عرض جميع البيانات
   ```

2. **فعل البحث الفوري بحذر**
   ```csharp
   searchPanel.EnableRealTimeSearch = false; // للبيانات الكبيرة
   ```

3. **استخدم التحديد المتعدد بحكمة**
   ```csharp
   advancedGrid.EnableMultiSelect = true; // فقط عند الحاجة
   ```

## 🎉 الخلاصة

تم تطوير نظام جداول متقدم ومتكامل يوفر:

- ✅ تجربة مستخدم محسنة
- ✅ أداء عالي مع البيانات الكبيرة
- ✅ مرونة في التخصيص
- ✅ وظائف متقدمة للبحث والفلترة
- ✅ تصدير وطباعة احترافية
- ✅ سهولة في الاستخدام والتطبيق

**النظام جاهز للاستخدام الإنتاجي ويمكن تطبيقه على جميع وحدات النظام!** 🚀
