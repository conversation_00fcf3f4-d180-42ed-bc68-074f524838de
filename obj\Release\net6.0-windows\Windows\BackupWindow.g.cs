﻿#pragma checksum "..\..\..\..\Windows\BackupWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "26300047470E6CFC14C26D8411972D221CEE8D86"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// BackupWindow
    /// </summary>
    public partial class BackupWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 31 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AutoBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanupBtn;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BackupsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateManualBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar BackupProgressBar;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalBackupsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessfulBackupsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Windows\BackupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/backupwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\BackupWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CreateBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Windows\BackupWindow.xaml"
            this.CreateBackupBtn.Click += new System.Windows.RoutedEventHandler(this.CreateBackupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AutoBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\Windows\BackupWindow.xaml"
            this.AutoBackupBtn.Click += new System.Windows.RoutedEventHandler(this.AutoBackupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\Windows\BackupWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CleanupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\Windows\BackupWindow.xaml"
            this.CleanupBtn.Click += new System.Windows.RoutedEventHandler(this.CleanupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BackupsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 56 "..\..\..\..\Windows\BackupWindow.xaml"
            this.BackupsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BackupsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BackupNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.CreateManualBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 147 "..\..\..\..\Windows\BackupWindow.xaml"
            this.CreateManualBackupBtn.Click += new System.Windows.RoutedEventHandler(this.CreateManualBackupBtn_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BackupProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 15:
            this.TotalBackupsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.SuccessfulBackupsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TotalSizeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 101 "..\..\..\..\Windows\BackupWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreBtn_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 105 "..\..\..\..\Windows\BackupWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.VerifyBtn_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 109 "..\..\..\..\Windows\BackupWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFolderBtn_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 113 "..\..\..\..\Windows\BackupWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteBackupBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

