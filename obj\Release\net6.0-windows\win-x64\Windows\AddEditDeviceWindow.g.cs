﻿#pragma checksum "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "59C25189F9730A6D79470AF1887B475F3ACE6584"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditDeviceWindow
    /// </summary>
    public partial class AddEditDeviceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 15 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BrandTextBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewSerialNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewComponentNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSerialBtn;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SerialNumbersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageCategoriesBtn;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SellingPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PurchaseDatePicker;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker WarrantyStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker WarrantyEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LocationComboBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UserManualPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaintenanceManualPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OriginCertificatePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QualityCertificatePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseQualityCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearQualityCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficialCertificationsPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TechnicalInfoBookletPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseTechnicalInfoBookletBtn;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearTechnicalInfoBookletBtn;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ImportPapersPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseImportPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 355 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearImportPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContractPapersPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseContractPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearContractPapersBtn;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditdevicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.BrandTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.ModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.NewSerialNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.NewComponentNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.AddSerialBtn = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.AddSerialBtn.Click += new System.Windows.RoutedEventHandler(this.AddSerialBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SerialNumbersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.SerialCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.ManageCategoriesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ManageCategoriesBtn.Click += new System.Windows.RoutedEventHandler(this.ManageCategoriesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SellingPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.PurchaseDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 17:
            this.WarrantyStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 18:
            this.WarrantyEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 19:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.LocationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 21:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.UserManualPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.BrowseUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.ClearUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 237 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.ClearUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.MaintenanceManualPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.BrowseMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 254 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.ClearMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 257 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.ClearMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.OriginCertificatePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.BrowseOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 274 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.ClearOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 277 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.ClearOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.QualityCertificatePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.BrowseQualityCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseQualityCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.ClearQualityCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearQualityCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.ClearQualityCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.OfficialCertificationsPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.BrowseOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 314 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.ClearOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 317 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.ClearOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.TechnicalInfoBookletPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 38:
            this.BrowseTechnicalInfoBookletBtn = ((System.Windows.Controls.Button)(target));
            
            #line 334 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseTechnicalInfoBookletBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.ClearTechnicalInfoBookletBtn = ((System.Windows.Controls.Button)(target));
            
            #line 337 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearTechnicalInfoBookletBtn.Click += new System.Windows.RoutedEventHandler(this.ClearTechnicalInfoBookletBtn_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.ImportPapersPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 41:
            this.BrowseImportPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 354 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseImportPapersBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.ClearImportPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 357 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearImportPapersBtn.Click += new System.Windows.RoutedEventHandler(this.ClearImportPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.ContractPapersPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 44:
            this.BrowseContractPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 374 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseContractPapersBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.ClearContractPapersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 377 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearContractPapersBtn.Click += new System.Windows.RoutedEventHandler(this.ClearContractPapersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 388 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 391 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 105 "..\..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveSerialBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

