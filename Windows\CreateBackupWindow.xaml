<Window x:Class="MedicalDevicesManager.Windows.CreateBackupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء نسخة احتياطية" Height="300" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="💾 إنشاء نسخة احتياطية جديدة" 
                   FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- اسم النسخة الاحتياطية -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="اسم النسخة الاحتياطية:" VerticalAlignment="Center" 
                       Margin="0,0,15,0" FontWeight="SemiBold"/>
            <TextBox Grid.Column="1" x:Name="BackupNameTextBox" Height="30" FontSize="14"/>
        </Grid>
        
        <!-- الوصف -->
        <Grid Grid.Row="2" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="الوصف:" VerticalAlignment="Top" 
                       Margin="0,5,15,0" FontWeight="SemiBold"/>
            <TextBox Grid.Column="1" x:Name="DescriptionTextBox" Height="60" FontSize="14"
                     TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
        </Grid>
        
        <!-- خيارات إضافية -->
        <GroupBox Grid.Row="3" Header="خيارات إضافية" Margin="0,0,0,15" Padding="10">
            <StackPanel>
                <CheckBox x:Name="CompressBackupCheckBox" Content="ضغط النسخة الاحتياطية" 
                          Margin="0,0,0,5" IsChecked="False"/>
                <CheckBox x:Name="VerifyBackupCheckBox" Content="التحقق من سلامة النسخة بعد الإنشاء" 
                          Margin="0,0,0,5" IsChecked="True"/>
                <CheckBox x:Name="DeleteOldBackupsCheckBox" Content="حذف النسخ القديمة (الاحتفاظ بآخر 10)" 
                          Margin="0,0,0,5" IsChecked="False"/>
            </StackPanel>
        </GroupBox>
        
        <!-- الأزرار -->
        <Grid Grid.Row="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="1" x:Name="CreateBtn" Content="💾 إنشاء" Width="100" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" 
                    Click="CreateBtn_Click" IsDefault="True"/>
            
            <Button Grid.Column="2" x:Name="CancelBtn" Content="❌ إلغاء" Width="100" Height="35" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" 
                    Click="CancelBtn_Click" IsCancel="True"/>
        </Grid>
    </Grid>
</Window>
