using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Services
{
    public class SmartSearchService
    {
        private readonly MedicalDevicesContext _context;

        public SmartSearchService(MedicalDevicesContext context)
        {
            _context = context;
        }

        // بناء فهرس البحث
        public async Task BuildSearchIndexAsync()
        {
            try
            {
                // حذف الفهرس القديم
                var oldIndexes = await _context.SearchIndexes.ToListAsync();
                _context.SearchIndexes.RemoveRange(oldIndexes);

                // فهرسة الأجهزة الطبية
                await IndexMedicalDevicesAsync();

                // فهرسة العملاء
                await IndexCustomersAsync();

                // فهرسة المبيعات
                await IndexSalesAsync();

                // فهرسة التنصيبات
                await IndexInstallationsAsync();

                // فهرسة المخزون
                await IndexInventoryAsync();

                // فهرسة الموردين
                await IndexSuppliersAsync();

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في بناء فهرس البحث: {ex.Message}");
            }
        }

        // فهرسة الأجهزة الطبية
        private async Task IndexMedicalDevicesAsync()
        {
            var devices = await _context.MedicalDevices.ToListAsync();

            foreach (var device in devices)
            {
                var searchableText = $"{device.Name} {device.Brand} {device.Model} {device.SerialNumber} {device.Category} {device.Description} {device.Supplier} {device.Location}";
                var keywords = $"{device.Brand},{device.Model},{device.Category},{device.Status}";

                var index = new SearchIndex
                {
                    ItemType = "Device",
                    ItemId = device.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = device.Category,
                    NumericValue1 = device.PurchasePrice,
                    NumericValue2 = device.SellingPrice,
                    DateValue1 = device.PurchaseDate,
                    DateValue2 = device.WarrantyEndDate,
                    Status = device.Status,
                    Tags = $"جهاز,طبي,{device.Category}",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // فهرسة العملاء
        private async Task IndexCustomersAsync()
        {
            var customers = await _context.Customers.ToListAsync();

            foreach (var customer in customers)
            {
                var searchableText = $"{customer.Name} {customer.CustomerType} {customer.Phone} {customer.Email} {customer.Address} {customer.City}";
                var keywords = $"{customer.CustomerType},{customer.City},{customer.Status}";

                var index = new SearchIndex
                {
                    ItemType = "Customer",
                    ItemId = customer.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = customer.CustomerType,
                    NumericValue1 = customer.CreditLimit,
                    Status = customer.Status,
                    Tags = $"عميل,{customer.CustomerType}",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // فهرسة المبيعات
        private async Task IndexSalesAsync()
        {
            var sales = await _context.Sales.ToListAsync();

            foreach (var sale in sales)
            {
                var searchableText = $"{sale.InvoiceNumber} {sale.CustomerName} {sale.DeviceName}";
                var keywords = $"{sale.PaymentStatus},{sale.CustomerName}";

                var index = new SearchIndex
                {
                    ItemType = "Sale",
                    ItemId = sale.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = "مبيعات",
                    NumericValue1 = sale.TotalAmount,
                    NumericValue2 = sale.FinalAmount,
                    DateValue1 = sale.SaleDate,
                    Status = sale.PaymentStatus,
                    Tags = $"مبيعة,فاتورة",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // فهرسة التنصيبات
        private async Task IndexInstallationsAsync()
        {
            var installations = await _context.DeviceInstallations.ToListAsync();

            foreach (var installation in installations)
            {
                var searchableText = $"{installation.DeviceName} {installation.CustomerName} {installation.SerialNumber} {installation.Model} {installation.Manufacturer} {installation.InstallationLocation} {installation.ContactPersonName}";
                var keywords = $"{installation.Manufacturer},{installation.CustomerName},{installation.InstallationStatus}";

                var index = new SearchIndex
                {
                    ItemType = "Installation",
                    ItemId = installation.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = "تنصيب",
                    NumericValue1 = installation.InstallationCost,
                    NumericValue2 = installation.WarrantyYears,
                    DateValue1 = installation.InstallationDate,
                    DateValue2 = installation.WarrantyEndDate,
                    Status = installation.InstallationStatus,
                    Tags = $"تنصيب,جهاز",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // فهرسة المخزون
        private async Task IndexInventoryAsync()
        {
            var inventory = await _context.InventoryItems.ToListAsync();

            foreach (var item in inventory)
            {
                var searchableText = $"{item.Name} {item.Category} {item.Description} {item.Location} {item.Unit}";
                var keywords = $"{item.Category},{item.Status},{item.Unit}";

                var index = new SearchIndex
                {
                    ItemType = "Inventory",
                    ItemId = item.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = item.Category,
                    NumericValue1 = item.CurrentStock,
                    NumericValue2 = item.UnitPrice,
                    DateValue1 = item.ExpiryDate,
                    Status = item.Status,
                    Tags = $"مخزون,{item.Category}",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // فهرسة الموردين
        private async Task IndexSuppliersAsync()
        {
            var suppliers = await _context.Suppliers.ToListAsync();

            foreach (var supplier in suppliers)
            {
                var searchableText = $"{supplier.Name} {supplier.Phone} {supplier.Email} {supplier.Address} {supplier.City}";
                var keywords = $"{supplier.City},{supplier.Status}";

                var index = new SearchIndex
                {
                    ItemType = "Supplier",
                    ItemId = supplier.Id,
                    SearchableText = searchableText.ToLower(),
                    Keywords = keywords.ToLower(),
                    Category = "مورد",
                    NumericValue1 = (decimal)supplier.Rating,
                    Status = supplier.Status,
                    Tags = $"مورد,شركة",
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                _context.SearchIndexes.Add(index);
            }
        }

        // البحث الذكي
        public async Task<List<SearchResult>> SmartSearchAsync(string query, int maxResults = 50)
        {
            if (string.IsNullOrWhiteSpace(query))
                return new List<SearchResult>();

            try
            {
                var searchTerms = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
                var results = new List<SearchResult>();

                var indexes = await _context.SearchIndexes
                    .Where(i => searchTerms.Any(term => i.SearchableText.Contains(term) || i.Keywords.Contains(term)))
                    .OrderByDescending(i => i.SearchCount)
                    .Take(maxResults)
                    .ToListAsync();

                foreach (var index in indexes)
                {
                    // حساب نقاط الصلة
                    var relevanceScore = CalculateRelevanceScore(index, searchTerms);

                    var result = new SearchResult
                    {
                        ItemType = index.ItemType,
                        ItemId = index.ItemId,
                        Title = await GetItemTitleAsync(index.ItemType, index.ItemId),
                        Description = await GetItemDescriptionAsync(index.ItemType, index.ItemId),
                        Category = index.Category,
                        Status = index.Status,
                        RelevanceScore = relevanceScore,
                        MatchedTerms = GetMatchedTerms(index, searchTerms)
                    };

                    results.Add(result);

                    // تحديث عداد البحث
                    index.SearchCount++;
                    index.LastSearched = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                return results.OrderByDescending(r => r.RelevanceScore).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في البحث الذكي: {ex.Message}");
                return new List<SearchResult>();
            }
        }

        // حساب نقاط الصلة
        private int CalculateRelevanceScore(SearchIndex index, string[] searchTerms)
        {
            var score = 0;

            foreach (var term in searchTerms)
            {
                // نقاط إضافية للمطابقة في الكلمات المفتاحية
                if (index.Keywords.Contains(term))
                    score += 10;

                // نقاط للمطابقة في النص القابل للبحث
                if (index.SearchableText.Contains(term))
                    score += 5;

                // نقاط إضافية للمطابقة الدقيقة
                if (index.SearchableText.Split(' ').Contains(term))
                    score += 3;
            }

            // نقاط إضافية للعناصر المبحوث عنها كثيراً
            score += index.SearchCount / 10;

            return score;
        }

        // الحصول على عنوان العنصر
        private async Task<string> GetItemTitleAsync(string itemType, int itemId)
        {
            try
            {
                return itemType switch
                {
                    "Device" => (await _context.MedicalDevices.FindAsync(itemId))?.Name ?? "جهاز غير معروف",
                    "Customer" => (await _context.Customers.FindAsync(itemId))?.Name ?? "عميل غير معروف",
                    "Sale" => (await _context.Sales.FindAsync(itemId))?.InvoiceNumber ?? "فاتورة غير معروفة",
                    "Installation" => $"تنصيب {(await _context.DeviceInstallations.FindAsync(itemId))?.DeviceName ?? "غير معروف"}",
                    "Inventory" => (await _context.InventoryItems.FindAsync(itemId))?.Name ?? "صنف غير معروف",
                    "Supplier" => (await _context.Suppliers.FindAsync(itemId))?.Name ?? "مورد غير معروف",
                    _ => "عنصر غير معروف"
                };
            }
            catch
            {
                return "عنصر غير معروف";
            }
        }

        // الحصول على وصف العنصر
        private async Task<string> GetItemDescriptionAsync(string itemType, int itemId)
        {
            try
            {
                return itemType switch
                {
                    "Device" => $"{(await _context.MedicalDevices.FindAsync(itemId))?.Brand} - {(await _context.MedicalDevices.FindAsync(itemId))?.Model}",
                    "Customer" => (await _context.Customers.FindAsync(itemId))?.CustomerType ?? "",
                    "Sale" => $"مبيعة بقيمة {(await _context.Sales.FindAsync(itemId))?.FinalAmount:N0} د.ع",
                    "Installation" => $"منصب لدى {(await _context.DeviceInstallations.FindAsync(itemId))?.CustomerName}",
                    "Inventory" => $"الكمية المتاحة: {(await _context.InventoryItems.FindAsync(itemId))?.CurrentStock}",
                    "Supplier" => (await _context.Suppliers.FindAsync(itemId))?.City ?? "",
                    _ => ""
                };
            }
            catch
            {
                return "";
            }
        }

        // الحصول على المصطلحات المطابقة
        private List<string> GetMatchedTerms(SearchIndex index, string[] searchTerms)
        {
            var matchedTerms = new List<string>();

            foreach (var term in searchTerms)
            {
                if (index.SearchableText.Contains(term) || index.Keywords.Contains(term))
                {
                    matchedTerms.Add(term);
                }
            }

            return matchedTerms;
        }

        // اقتراحات البحث
        public async Task<List<string>> GetSearchSuggestionsAsync(string partialQuery, int maxSuggestions = 10)
        {
            if (string.IsNullOrWhiteSpace(partialQuery) || partialQuery.Length < 2)
                return new List<string>();

            try
            {
                var indexes = await _context.SearchIndexes
                    .Where(i => i.SearchableText.Contains(partialQuery.ToLower()))
                    .ToListAsync();

                var suggestions = indexes
                    .SelectMany(i => i.SearchableText.Split(' ', StringSplitOptions.RemoveEmptyEntries))
                    .Where(word => word.StartsWith(partialQuery.ToLower()) && word.Length > partialQuery.Length)
                    .Distinct()
                    .Take(maxSuggestions)
                    .ToList();

                return suggestions;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اقتراحات البحث: {ex.Message}");
                return new List<string>();
            }
        }
    }

    // نتيجة البحث
    public class SearchResult
    {
        public string ItemType { get; set; } = "";
        public int ItemId { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public string Status { get; set; } = "";
        public int RelevanceScore { get; set; }
        public List<string> MatchedTerms { get; set; } = new List<string>();
    }
}

namespace MedicalDevicesManager.Services
{
    public class LiveStatisticsService
    {
        private readonly MedicalDevicesContext _context;

        public LiveStatisticsService(MedicalDevicesContext context)
        {
            _context = context;
        }

        // حساب جميع الإحصائيات
        public async Task CalculateAllStatisticsAsync()
        {
            await CalculateSalesStatisticsAsync();
            await CalculateInventoryStatisticsAsync();
            await CalculateDeviceStatisticsAsync();
            await CalculateCustomerStatisticsAsync();
            await CalculateFinancialStatisticsAsync();
        }

        // إحصائيات المبيعات
        private async Task CalculateSalesStatisticsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var yesterday = today.AddDays(-1);
                var thisWeek = today.AddDays(-(int)today.DayOfWeek);
                var lastWeek = thisWeek.AddDays(-7);
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var lastMonth = thisMonth.AddMonths(-1);

                // مبيعات اليوم
                var todaySales = await _context.Sales
                    .Where(s => s.SaleDate.Date == today)
                    .SumAsync(s => s.FinalAmount);

                var yesterdaySales = await _context.Sales
                    .Where(s => s.SaleDate.Date == yesterday)
                    .SumAsync(s => s.FinalAmount);

                await UpdateStatistic("sales", "daily_sales", todaySales, yesterdaySales, "daily");

                // مبيعات الأسبوع
                var thisWeekSales = await _context.Sales
                    .Where(s => s.SaleDate >= thisWeek)
                    .SumAsync(s => s.FinalAmount);

                var lastWeekSales = await _context.Sales
                    .Where(s => s.SaleDate >= lastWeek && s.SaleDate < thisWeek)
                    .SumAsync(s => s.FinalAmount);

                await UpdateStatistic("sales", "weekly_sales", thisWeekSales, lastWeekSales, "weekly");

                // مبيعات الشهر
                var thisMonthSales = await _context.Sales
                    .Where(s => s.SaleDate >= thisMonth)
                    .SumAsync(s => s.FinalAmount);

                var lastMonthSales = await _context.Sales
                    .Where(s => s.SaleDate >= lastMonth && s.SaleDate < thisMonth)
                    .SumAsync(s => s.FinalAmount);

                await UpdateStatistic("sales", "monthly_sales", thisMonthSales, lastMonthSales, "monthly");

                // عدد الفواتير
                var todayInvoices = await _context.Sales
                    .CountAsync(s => s.SaleDate.Date == today);

                var yesterdayInvoices = await _context.Sales
                    .CountAsync(s => s.SaleDate.Date == yesterday);

                await UpdateStatistic("sales", "daily_invoices", todayInvoices, yesterdayInvoices, "daily");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب إحصائيات المبيعات: {ex.Message}");
            }
        }

        // إحصائيات المخزون
        private async Task CalculateInventoryStatisticsAsync()
        {
            try
            {
                // إجمالي قيمة المخزون
                var totalInventoryValue = await _context.InventoryItems
                    .Where(i => i.Status == "متاح")
                    .SumAsync(i => i.CurrentStock * i.UnitPrice);

                await UpdateStatistic("inventory", "total_value", totalInventoryValue, 0, "daily");

                // عدد الأصناف
                var totalItems = await _context.InventoryItems
                    .CountAsync(i => i.Status == "متاح");

                await UpdateStatistic("inventory", "total_items", totalItems, 0, "daily");

                // الأصناف المنخفضة
                var lowStockItems = await _context.InventoryItems
                    .CountAsync(i => i.CurrentStock <= i.MinimumStock && i.Status == "متاح");

                await UpdateStatistic("inventory", "low_stock_items", lowStockItems, 0, "daily");

                // الأصناف المنتهية الصلاحية
                var expiredItems = await _context.InventoryItems
                    .CountAsync(i => i.ExpiryDate.HasValue && i.ExpiryDate <= DateTime.Now && i.Status == "متاح");

                await UpdateStatistic("inventory", "expired_items", expiredItems, 0, "daily");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب إحصائيات المخزون: {ex.Message}");
            }
        }

        // إحصائيات الأجهزة
        private async Task CalculateDeviceStatisticsAsync()
        {
            try
            {
                // إجمالي الأجهزة
                var totalDevices = await _context.MedicalDevices.CountAsync();
                await UpdateStatistic("devices", "total_devices", totalDevices, 0, "daily");

                // الأجهزة المتاحة
                var availableDevices = await _context.MedicalDevices
                    .CountAsync(d => d.Status == "متاح");
                await UpdateStatistic("devices", "available_devices", availableDevices, 0, "daily");

                // الأجهزة المباعة
                var soldDevices = await _context.MedicalDevices
                    .CountAsync(d => d.Status == "مباع");
                await UpdateStatistic("devices", "sold_devices", soldDevices, 0, "daily");

                // التنصيبات النشطة
                var activeInstallations = await _context.DeviceInstallations
                    .CountAsync(i => i.IsActive && i.InstallationStatus == "مكتمل");
                await UpdateStatistic("devices", "active_installations", activeInstallations, 0, "daily");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب إحصائيات الأجهزة: {ex.Message}");
            }
        }

        // إحصائيات العملاء
        private async Task CalculateCustomerStatisticsAsync()
        {
            try
            {
                // إجمالي العملاء
                var totalCustomers = await _context.Customers
                    .CountAsync(c => c.Status == "نشط");
                await UpdateStatistic("customers", "total_customers", totalCustomers, 0, "daily");

                // عملاء جدد هذا الشهر
                var thisMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var newCustomers = await _context.Customers
                    .CountAsync(c => c.CreatedDate >= thisMonth);
                await UpdateStatistic("customers", "new_customers_month", newCustomers, 0, "monthly");

                // العملاء النشطين (لديهم مبيعات في آخر 30 يوم)
                var activeCustomers = await _context.Sales
                    .Where(s => s.SaleDate >= DateTime.Now.AddDays(-30))
                    .Select(s => s.CustomerId)
                    .Distinct()
                    .CountAsync();
                await UpdateStatistic("customers", "active_customers", activeCustomers, 0, "daily");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب إحصائيات العملاء: {ex.Message}");
            }
        }

        // الإحصائيات المالية
        private async Task CalculateFinancialStatisticsAsync()
        {
            try
            {
                var thisMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var lastMonth = thisMonth.AddMonths(-1);

                // الإيرادات الشهرية
                var thisMonthRevenue = await _context.Sales
                    .Where(s => s.SaleDate >= thisMonth)
                    .SumAsync(s => s.FinalAmount);

                var lastMonthRevenue = await _context.Sales
                    .Where(s => s.SaleDate >= lastMonth && s.SaleDate < thisMonth)
                    .SumAsync(s => s.FinalAmount);

                await UpdateStatistic("financial", "monthly_revenue", thisMonthRevenue, lastMonthRevenue, "monthly");

                // الأرباح المتوقعة (الإيرادات - تكلفة الشراء)
                var thisMonthProfit = await _context.Sales
                    .Where(s => s.SaleDate >= thisMonth)
                    .SumAsync(s => s.FinalAmount - (s.Quantity * s.UnitPrice * 0.7m)); // تقدير 30% ربح

                var lastMonthProfit = await _context.Sales
                    .Where(s => s.SaleDate >= lastMonth && s.SaleDate < thisMonth)
                    .SumAsync(s => s.FinalAmount - (s.Quantity * s.UnitPrice * 0.7m));

                await UpdateStatistic("financial", "monthly_profit", thisMonthProfit, lastMonthProfit, "monthly");

                // المدفوعات المعلقة
                var pendingPayments = await _context.Sales
                    .Where(s => s.PaymentStatus == "جزئي" || s.PaymentStatus == "غير مدفوع")
                    .SumAsync(s => s.FinalAmount); // إجمالي المبالغ غير المدفوعة

                await UpdateStatistic("financial", "pending_payments", pendingPayments, 0, "daily");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حساب الإحصائيات المالية: {ex.Message}");
            }
        }

        // تحديث إحصائية
        private async Task UpdateStatistic(string statType, string statName, decimal currentValue,
            decimal previousValue, string period)
        {
            try
            {
                var existing = await _context.LiveStatistics
                    .FirstOrDefaultAsync(s => s.StatType == statType && s.StatName == statName);

                var changeValue = currentValue - previousValue;
                var changePercentage = previousValue != 0 ? (changeValue / previousValue) * 100 : 0;
                var trend = changeValue > 0 ? "up" : changeValue < 0 ? "down" : "stable";

                if (existing != null)
                {
                    existing.PreviousValue = existing.CurrentValue;
                    existing.CurrentValue = currentValue;
                    existing.ChangeValue = changeValue;
                    existing.ChangePercentage = changePercentage;
                    existing.Trend = trend;
                    existing.Period = period;
                    existing.CalculatedDate = DateTime.Now;
                    existing.LastUpdated = DateTime.Now;
                }
                else
                {
                    var newStat = new LiveStatistics
                    {
                        StatType = statType,
                        StatName = statName,
                        CurrentValue = currentValue,
                        PreviousValue = previousValue,
                        ChangeValue = changeValue,
                        ChangePercentage = changePercentage,
                        Trend = trend,
                        Period = period,
                        CalculatedDate = DateTime.Now,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    _context.LiveStatistics.Add(newStat);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الإحصائية: {ex.Message}");
            }
        }

        // الحصول على الإحصائيات
        public async Task<List<LiveStatistics>> GetStatisticsAsync(string statType = null)
        {
            try
            {
                var query = _context.LiveStatistics.Where(s => s.IsActive);

                if (!string.IsNullOrEmpty(statType))
                {
                    query = query.Where(s => s.StatType == statType);
                }

                return await query
                    .OrderBy(s => s.StatType)
                    .ThenBy(s => s.StatName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الإحصائيات: {ex.Message}");
                return new List<LiveStatistics>();
            }
        }
    }
}
