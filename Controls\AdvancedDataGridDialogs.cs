using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Collections.ObjectModel;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// النوافذ والحوارات للجدول المتقدم
    /// </summary>
    public partial class AdvancedDataGrid
    {
        #region Advanced Filter Dialog

        /// <summary>
        /// عرض نافذة الفلترة المتقدمة
        /// </summary>
        private void ShowAdvancedFilterDialog()
        {
            var dialog = new AdvancedFilterDialog(_mainDataGrid.Columns.Cast<DataGridColumn>().ToList(), _activeFilters);
            if (dialog.ShowDialog() == true)
            {
                _activeFilters = dialog.GetFilters();
                ApplyFilters();
            }
        }

        #endregion

        #region Column Customization Dialog

        /// <summary>
        /// عرض نافذة تخصيص الأعمدة
        /// </summary>
        private void ShowColumnCustomizationDialog()
        {
            var dialog = new ColumnCustomizationDialog(_mainDataGrid.Columns.Cast<DataGridColumn>().ToList());
            if (dialog.ShowDialog() == true)
            {
                dialog.ApplyChanges();
            }
        }

        #endregion
    }

    /// <summary>
    /// نافذة الفلترة المتقدمة
    /// </summary>
    public class AdvancedFilterDialog : Window
    {
        private Dictionary<string, object> _filters;
        private Dictionary<string, UIElement> _filterControls;
        private StackPanel _filtersPanel;

        public AdvancedFilterDialog(List<DataGridColumn> columns, Dictionary<string, object> currentFilters)
        {
            _filters = new Dictionary<string, object>(currentFilters);
            _filterControls = new Dictionary<string, UIElement>();
            
            InitializeDialog(columns);
        }

        private void InitializeDialog(List<DataGridColumn> columns)
        {
            Title = "فلترة متقدمة";
            Width = 500;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.NoResize;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // العنوان
            var title = new TextBlock
            {
                Text = "🔧 فلترة متقدمة",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            Grid.SetRow(title, 0);
            mainGrid.Children.Add(title);

            // منطقة الفلاتر
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(10)
            };

            _filtersPanel = new StackPanel();
            scrollViewer.Content = _filtersPanel;
            Grid.SetRow(scrollViewer, 1);
            mainGrid.Children.Add(scrollViewer);

            // إنشاء فلاتر للأعمدة
            CreateFilterControls(columns);

            // أزرار الإجراءات
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };

            var applyButton = new Button
            {
                Content = "تطبيق",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            applyButton.Click += ApplyButton_Click;

            var clearButton = new Button
            {
                Content = "مسح الكل",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            clearButton.Click += ClearButton_Click;

            var cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            cancelButton.Click += (s, e) => DialogResult = false;

            buttonsPanel.Children.Add(applyButton);
            buttonsPanel.Children.Add(clearButton);
            buttonsPanel.Children.Add(cancelButton);

            Grid.SetRow(buttonsPanel, 2);
            mainGrid.Children.Add(buttonsPanel);

            Content = mainGrid;
        }

        private void CreateFilterControls(List<DataGridColumn> columns)
        {
            foreach (var column in columns)
            {
                if (column is DataGridTextColumn textColumn && textColumn.Binding != null)
                {
                    var binding = textColumn.Binding as System.Windows.Data.Binding;
                    if (binding?.Path?.Path != null)
                    {
                        CreateFilterControl(column.Header?.ToString() ?? "", binding.Path.Path);
                    }
                }
            }
        }

        private void CreateFilterControl(string header, string propertyName)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(0, 5, 0, 5)
            };

            var label = new TextBlock
            {
                Text = header,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            panel.Children.Add(label);

            // إنشاء عنصر التحكم حسب نوع البيانات
            UIElement filterControl = CreateFilterControlByType(propertyName);
            if (filterControl is FrameworkElement fe)
                fe.Margin = new Thickness(0, 0, 0, 10);
            
            _filterControls[propertyName] = filterControl;
            panel.Children.Add(filterControl);

            _filtersPanel.Children.Add(panel);
        }

        private UIElement CreateFilterControlByType(string propertyName)
        {
            // تحديد نوع عنصر التحكم حسب اسم الخاصية
            if (propertyName.ToLower().Contains("date") || propertyName.ToLower().Contains("تاريخ"))
            {
                var datePicker = new DatePicker
                {
                    Width = 200,
                    Height = 30
                };
                
                if (_filters.ContainsKey(propertyName) && _filters[propertyName] is DateTime dateValue)
                {
                    datePicker.SelectedDate = dateValue;
                }
                
                return datePicker;
            }
            else if (propertyName.ToLower().Contains("price") || propertyName.ToLower().Contains("amount") || 
                     propertyName.ToLower().Contains("سعر") || propertyName.ToLower().Contains("مبلغ"))
            {
                var numericPanel = new StackPanel { Orientation = Orientation.Horizontal };
                
                var fromTextBox = new TextBox
                {
                    Width = 90,
                    Height = 30,
                    Margin = new Thickness(0, 0, 5, 0)
                };
                fromTextBox.SetValue(TextBox.TagProperty, "من");
                
                var toLabel = new TextBlock
                {
                    Text = "إلى",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(5, 0, 5, 0)
                };
                
                var toTextBox = new TextBox
                {
                    Width = 90,
                    Height = 30,
                    Margin = new Thickness(5, 0, 0, 0)
                };
                toTextBox.SetValue(TextBox.TagProperty, "إلى");
                
                numericPanel.Children.Add(fromTextBox);
                numericPanel.Children.Add(toLabel);
                numericPanel.Children.Add(toTextBox);
                
                return numericPanel;
            }
            else if (propertyName.ToLower().Contains("status") || propertyName.ToLower().Contains("حالة"))
            {
                var comboBox = new ComboBox
                {
                    Width = 200,
                    Height = 30
                };
                
                // إضافة خيارات الحالة الشائعة
                comboBox.Items.Add("");
                comboBox.Items.Add("نشط");
                comboBox.Items.Add("غير نشط");
                comboBox.Items.Add("متاح");
                comboBox.Items.Add("غير متاح");
                comboBox.Items.Add("مكتمل");
                comboBox.Items.Add("معلق");
                comboBox.Items.Add("ملغي");
                
                if (_filters.ContainsKey(propertyName))
                {
                    comboBox.SelectedItem = _filters[propertyName]?.ToString();
                }
                
                return comboBox;
            }
            else
            {
                var textBox = new TextBox
                {
                    Width = 200,
                    Height = 30
                };
                
                if (_filters.ContainsKey(propertyName))
                {
                    textBox.Text = _filters[propertyName]?.ToString() ?? "";
                }
                
                return textBox;
            }
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            _filters.Clear();
            
            foreach (var kvp in _filterControls)
            {
                var propertyName = kvp.Key;
                var control = kvp.Value;
                
                object filterValue = null;
                
                if (control is TextBox textBox && !string.IsNullOrWhiteSpace(textBox.Text))
                {
                    filterValue = textBox.Text;
                }
                else if (control is ComboBox comboBox && comboBox.SelectedItem != null && !string.IsNullOrEmpty(comboBox.SelectedItem.ToString()))
                {
                    filterValue = comboBox.SelectedItem.ToString();
                }
                else if (control is DatePicker datePicker && datePicker.SelectedDate.HasValue)
                {
                    filterValue = datePicker.SelectedDate.Value;
                }
                else if (control is StackPanel numericPanel)
                {
                    // معالجة النطاق الرقمي
                    var fromTextBox = numericPanel.Children.OfType<TextBox>().FirstOrDefault();
                    var toTextBox = numericPanel.Children.OfType<TextBox>().LastOrDefault();
                    
                    if (fromTextBox != null && !string.IsNullOrWhiteSpace(fromTextBox.Text))
                    {
                        if (decimal.TryParse(fromTextBox.Text, out decimal fromValue))
                        {
                            filterValue = fromValue;
                        }
                    }
                }
                
                if (filterValue != null)
                {
                    _filters[propertyName] = filterValue;
                }
            }
            
            DialogResult = true;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            _filters.Clear();
            
            foreach (var control in _filterControls.Values)
            {
                if (control is TextBox textBox)
                {
                    textBox.Text = "";
                }
                else if (control is ComboBox comboBox)
                {
                    comboBox.SelectedIndex = 0;
                }
                else if (control is DatePicker datePicker)
                {
                    datePicker.SelectedDate = null;
                }
                else if (control is StackPanel numericPanelClear)
                {
                    foreach (var clearTextBox in numericPanelClear.Children.OfType<TextBox>())
                    {
                        clearTextBox.Text = "";
                    }
                }
            }
        }

        public Dictionary<string, object> GetFilters()
        {
            return _filters;
        }
    }

    /// <summary>
    /// نافذة تخصيص الأعمدة
    /// </summary>
    public class ColumnCustomizationDialog : Window
    {
        private List<DataGridColumn> _columns;
        private ListBox _columnsListBox;
        private List<ColumnInfo> _columnInfos;

        public ColumnCustomizationDialog(List<DataGridColumn> columns)
        {
            _columns = columns;
            _columnInfos = new List<ColumnInfo>();
            
            InitializeDialog();
            LoadColumns();
        }

        private void InitializeDialog()
        {
            Title = "تخصيص الأعمدة";
            Width = 400;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.NoResize;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // العنوان
            var title = new TextBlock
            {
                Text = "⚙️ تخصيص الأعمدة",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            Grid.SetRow(title, 0);
            mainGrid.Children.Add(title);

            // قائمة الأعمدة
            _columnsListBox = new ListBox
            {
                Margin = new Thickness(10),
                SelectionMode = SelectionMode.Single
            };
            Grid.SetRow(_columnsListBox, 1);
            mainGrid.Children.Add(_columnsListBox);

            // أزرار الإجراءات
            var buttonsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };

            var okButton = new Button
            {
                Content = "موافق",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            okButton.Click += (s, e) => DialogResult = true;

            var cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 35,
                Margin = new Thickness(5),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            cancelButton.Click += (s, e) => DialogResult = false;

            buttonsPanel.Children.Add(okButton);
            buttonsPanel.Children.Add(cancelButton);

            Grid.SetRow(buttonsPanel, 2);
            mainGrid.Children.Add(buttonsPanel);

            Content = mainGrid;
        }

        private void LoadColumns()
        {
            foreach (var column in _columns)
            {
                var columnInfo = new ColumnInfo
                {
                    Column = column,
                    Header = column.Header?.ToString() ?? "",
                    IsVisible = column.Visibility == Visibility.Visible,
                    Width = column.Width.Value
                };
                
                _columnInfos.Add(columnInfo);

                var checkBox = new CheckBox
                {
                    Content = columnInfo.Header,
                    IsChecked = columnInfo.IsVisible,
                    Margin = new Thickness(5),
                    Tag = columnInfo
                };

                _columnsListBox.Items.Add(checkBox);
            }
        }

        public void ApplyChanges()
        {
            foreach (CheckBox checkBox in _columnsListBox.Items)
            {
                if (checkBox.Tag is ColumnInfo columnInfo)
                {
                    columnInfo.Column.Visibility = checkBox.IsChecked == true ? Visibility.Visible : Visibility.Collapsed;
                }
            }
        }

        private class ColumnInfo
        {
            public DataGridColumn Column { get; set; }
            public string Header { get; set; }
            public bool IsVisible { get; set; }
            public double Width { get; set; }
        }
    }
}
