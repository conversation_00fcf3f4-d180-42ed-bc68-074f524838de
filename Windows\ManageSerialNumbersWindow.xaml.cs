using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Controls;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageSerialNumbersWindow : Window
    {
        private int _deviceId;
        private MedicalDevice _device;
        private ObservableCollection<DeviceSerialNumber> _serialNumbers;
        private DeviceSerialNumber _editingSerialNumber = null;
        private AdvancedDataGrid _advancedGrid;
        
        public ManageSerialNumbersWindow(MedicalDevice device)
        {
            InitializeComponent();
            _device = device;
            _deviceId = device.Id;
            InitializeAdvancedDataGrid();
            LoadDeviceInfo();
            _ = LoadSerialNumbersAsync(); // استدعاء غير متزامن
        }

        // كونستركتور إضافي للتوافق مع الكود القديم
        public ManageSerialNumbersWindow(int deviceId)
        {
            InitializeComponent();
            _deviceId = deviceId;
            InitializeAdvancedDataGrid();
            LoadDeviceInfoAsync();
            _ = LoadSerialNumbersAsync(); // استدعاء غير متزامن
        }
        
        private void LoadDeviceInfo()
        {
            if (_device != null)
            {
                DeviceNameTextBlock.Text = _device.Name;
                DeviceModelTextBlock.Text = _device.Model;
                Title = $"إدارة الأرقام التسلسلية - {_device.Name}";
            }
        }

        private async void LoadDeviceInfoAsync()
        {
            try
            {
                _device = await App.DatabaseContext.MedicalDevices
                    .FirstOrDefaultAsync(d => d.Id == _deviceId);

                if (_device != null)
                {
                    DeviceNameTextBlock.Text = _device.Name;
                    DeviceModelTextBlock.Text = _device.Model;
                    Title = $"إدارة الأرقام التسلسلية - {_device.Name}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeAdvancedDataGrid()
        {
            try
            {
                _advancedGrid = new AdvancedDataGrid();

                // إعداد الأعمدة
                _advancedGrid.AddTextColumn("الرقم التسلسلي", "SerialNumber", 150);
                _advancedGrid.AddTextColumn("اسم المكون", "ComponentName", 150);
                _advancedGrid.AddTextColumn("نوع المكون", "ComponentType", 120);
                _advancedGrid.AddTextColumn("الوصف", "Description", 200);
                _advancedGrid.AddTextColumn("الموقع", "Location", 120);
                _advancedGrid.AddTextColumn("الحالة", "Status", 100);
                _advancedGrid.AddDateColumn("تاريخ التركيب", "InstallationDate", "dd/MM/yyyy", 120);
                _advancedGrid.AddTextColumn("الملاحظات", "Notes", 200);

                // إعداد الأحداث
                _advancedGrid.EditRequested += (sender, selectedItem) =>
                {
                    if (selectedItem is DeviceSerialNumber serialNumber)
                    {
                        EditSerialNumber(serialNumber);
                    }
                };

                _advancedGrid.DeleteRequested += async (sender, selectedItem) =>
                {
                    if (selectedItem is DeviceSerialNumber serialNumber)
                    {
                        await DeleteSerialNumber(serialNumber);
                    }
                };

                _advancedGrid.ViewRequested += (sender, selectedItem) =>
                {
                    if (selectedItem is DeviceSerialNumber serialNumber)
                    {
                        ShowSerialNumberDetails(serialNumber);
                    }
                };

                _advancedGrid.DataRefreshRequested += (sender, e) =>
                {
                    _ = LoadSerialNumbersAsync();
                };

                // إضافة الجدول إلى ScrollViewer
                SerialNumbersScrollViewer.Content = _advancedGrid;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الجدول: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSerialNumbersAsync()
        {
            try
            {
                // إعادة تحميل البيانات من قاعدة البيانات
                var serialNumbersList = await App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == _deviceId && s.IsActive)
                    .OrderBy(s => s.ComponentName)
                    .ToListAsync();

                // تحويل إلى ObservableCollection لضمان تحديث الواجهة
                _serialNumbers = new ObservableCollection<DeviceSerialNumber>(serialNumbersList);

                // تحديث AdvancedDataGrid
                if (_advancedGrid != null)
                {
                    _advancedGrid.SetDataSource(serialNumbersList);
                }

                // تحديث عداد الأرقام التسلسلية
                var activeCount = _serialNumbers.Count(s => s.Status == "نشط");
                var totalCount = _serialNumbers.Count;

                // البحث عن TextBlock لعرض العداد (إذا كان موجوداً)
                var countTextBlock = this.FindName("SerialNumbersCountTextBlock") as TextBlock;
                if (countTextBlock != null)
                {
                    countTextBlock.Text = $"إجمالي المكونات: {totalCount} | النشطة: {activeCount}";
                }

                // رسالة تأكيد للتطوير
                System.Diagnostics.Debug.WriteLine($"تم تحميل {totalCount} أرقام تسلسلية للجهاز {_deviceId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            _editingSerialNumber = null;
            ClearForm();
            EditPanelTitle.Text = "📝 إضافة رقم تسلسلي جديد";
            EditPanel.Visibility = Visibility.Visible;
            SerialNumberTextBox.Focus();
        }
        
        // تم نقل وظيفة التعديل إلى AdvancedDataGrid
        
        // تم نقل وظيفة الحذف إلى AdvancedDataGrid
        
        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!await ValidateFormAsync())
                return;

            try
            {
                if (_editingSerialNumber == null)
                {
                    // إضافة رقم تسلسلي جديد
                    var newSerial = new DeviceSerialNumber
                    {
                        DeviceId = _deviceId,
                        SerialNumber = SerialNumberTextBox.Text.Trim(),
                        ComponentName = ComponentNameTextBox.Text.Trim(),
                        ComponentType = (ComponentTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "رئيسي",
                        Description = DescriptionTextBox.Text.Trim(),
                        Location = LocationTextBox.Text.Trim(),
                        Status = (StatusComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "نشط",
                        InstallationDate = InstallationDatePicker.SelectedDate,
                        Notes = NotesTextBox.Text.Trim(),
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    App.DatabaseContext.DeviceSerialNumbers.Add(newSerial);

                    // حفظ التغييرات مع معالجة الأخطاء المحسنة
                    var saveResult = await App.DatabaseContext.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine($"نتيجة الحفظ: {saveResult} صفوف متأثرة");
                    System.Diagnostics.Debug.WriteLine($"تم إضافة رقم تسلسلي: {newSerial.SerialNumber} للجهاز {newSerial.DeviceId}");

                    if (saveResult > 0)
                    {
                        MessageBox.Show("تم إضافة الرقم التسلسلي بنجاح!", "نجحت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);

                        // إعادة تحميل البيانات لضمان التحديث الكامل
                        await LoadSerialNumbersAsync();
                        ClearForm();
                        EditPanel.Visibility = Visibility.Collapsed;
                    }
                    else
                    {
                        MessageBox.Show("لم يتم حفظ أي تغييرات. يرجى المحاولة مرة أخرى.", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    // تعديل رقم تسلسلي موجود
                    _editingSerialNumber.SerialNumber = SerialNumberTextBox.Text.Trim();
                    _editingSerialNumber.ComponentName = ComponentNameTextBox.Text.Trim();
                    _editingSerialNumber.ComponentType = (ComponentTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "رئيسي";
                    _editingSerialNumber.Description = DescriptionTextBox.Text.Trim();
                    _editingSerialNumber.Location = LocationTextBox.Text.Trim();
                    _editingSerialNumber.Status = (StatusComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "نشط";
                    _editingSerialNumber.InstallationDate = InstallationDatePicker.SelectedDate;
                    _editingSerialNumber.Notes = NotesTextBox.Text.Trim();
                    _editingSerialNumber.LastUpdated = DateTime.Now;

                    var saveResult = await App.DatabaseContext.SaveChangesAsync();

                    if (saveResult > 0)
                    {
                        MessageBox.Show("تم تعديل الرقم التسلسلي بنجاح!", "نجح التعديل", MessageBoxButton.OK, MessageBoxImage.Information);

                        // إعادة تحميل البيانات وإخفاء نموذج التحرير
                        await LoadSerialNumbersAsync();
                        ClearForm();
                        EditPanel.Visibility = Visibility.Collapsed;
                    }
                    else
                    {
                        MessageBox.Show("لم يتم حفظ أي تغييرات. يرجى المحاولة مرة أخرى.", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الرقم التسلسلي: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
        
        // تم استبدال DataGrid بـ AdvancedDataGrid - لا حاجة لهذه الدالة
        
        // تم نقل وظيفة تحميل البيانات إلى EditSerialNumber في AdvancedDataGrid
        
        private void ClearForm()
        {
            _editingSerialNumber = null;
            EditPanel.Visibility = Visibility.Collapsed;
            
            SerialNumberTextBox.Text = "";
            ComponentNameTextBox.Text = "";
            DescriptionTextBox.Text = "";
            LocationTextBox.Text = "";
            NotesTextBox.Text = "";
            InstallationDatePicker.SelectedDate = null;
            ComponentTypeComboBox.SelectedIndex = 0;
            StatusComboBox.SelectedIndex = 0;
        }
        
        private async Task<bool> ValidateFormAsync()
        {
            if (string.IsNullOrWhiteSpace(SerialNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم التسلسلي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ComponentNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكون", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ComponentNameTextBox.Focus();
                return false;
            }

            // التحقق من عدم تكرار الرقم التسلسلي مباشرة من قاعدة البيانات
            var serialNumberToCheck = SerialNumberTextBox.Text.Trim();
            var existingSerial = await App.DatabaseContext.DeviceSerialNumbers
                .FirstOrDefaultAsync(s =>
                    s.DeviceId == _deviceId &&
                    s.IsActive &&
                    s.SerialNumber.ToLower() == serialNumberToCheck.ToLower() &&
                    (_editingSerialNumber == null || s.Id != _editingSerialNumber.Id));

            if (existingSerial != null)
            {
                MessageBox.Show("هذا الرقم التسلسلي موجود بالفعل لهذا الجهاز", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }

            return true;
        }

        private void EditSerialNumber(DeviceSerialNumber serialNumber)
        {
            try
            {
                _editingSerialNumber = serialNumber;

                // ملء النموذج بالبيانات
                SerialNumberTextBox.Text = serialNumber.SerialNumber;
                ComponentNameTextBox.Text = serialNumber.ComponentName;
                DescriptionTextBox.Text = serialNumber.Description ?? "";
                LocationTextBox.Text = serialNumber.Location ?? "";
                NotesTextBox.Text = serialNumber.Notes ?? "";
                InstallationDatePicker.SelectedDate = serialNumber.InstallationDate;

                // تحديد نوع المكون
                foreach (ComboBoxItem item in ComponentTypeComboBox.Items)
                {
                    if (item.Content.ToString() == serialNumber.ComponentType)
                    {
                        ComponentTypeComboBox.SelectedItem = item;
                        break;
                    }
                }

                // تحديد الحالة
                foreach (ComboBoxItem item in StatusComboBox.Items)
                {
                    if (item.Content.ToString() == serialNumber.Status)
                    {
                        StatusComboBox.SelectedItem = item;
                        break;
                    }
                }

                EditPanelTitle.Text = "✏️ تعديل الرقم التسلسلي";
                EditPanel.Visibility = Visibility.Visible;
                SerialNumberTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteSerialNumber(DeviceSerialNumber serialNumber)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الرقم التسلسلي '{serialNumber.SerialNumber}' للمكون '{serialNumber.ComponentName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    // حذف منطقي - تعيين IsActive إلى false
                    serialNumber.IsActive = false;
                    serialNumber.LastUpdated = DateTime.Now;

                    await App.DatabaseContext.SaveChangesAsync();

                    MessageBox.Show("تم حذف الرقم التسلسلي بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إعادة تحميل البيانات
                    await LoadSerialNumbersAsync();

                    // إخفاء نموذج التحرير إذا كان مفتوحاً
                    if (_editingSerialNumber?.Id == serialNumber.Id)
                    {
                        ClearForm();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الرقم التسلسلي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowSerialNumberDetails(DeviceSerialNumber serialNumber)
        {
            try
            {
                var details = $"تفاصيل الرقم التسلسلي:\n\n" +
                             $"الرقم التسلسلي: {serialNumber.SerialNumber}\n" +
                             $"اسم المكون: {serialNumber.ComponentName}\n" +
                             $"نوع المكون: {serialNumber.ComponentType}\n" +
                             $"الوصف: {serialNumber.Description}\n" +
                             $"الموقع: {serialNumber.Location}\n" +
                             $"الحالة: {serialNumber.Status}\n" +
                             $"تاريخ التركيب: {serialNumber.InstallationDate?.ToString("dd/MM/yyyy") ?? "غير محدد"}\n" +
                             $"الملاحظات: {serialNumber.Notes}\n" +
                             $"تاريخ الإنشاء: {serialNumber.CreatedDate:dd/MM/yyyy HH:mm}\n" +
                             $"آخر تحديث: {serialNumber.LastUpdated:dd/MM/yyyy HH:mm}";

                MessageBox.Show(details, "تفاصيل الرقم التسلسلي", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
