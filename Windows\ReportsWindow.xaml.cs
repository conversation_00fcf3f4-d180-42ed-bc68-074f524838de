using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Documents;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;

namespace MedicalDevicesManager.Windows
{
    public partial class ReportsWindow : Window
    {
        private bool _hasReportContent = false;

        public ReportsWindow()
        {
            InitializeComponent();
        }
        
        private async void SalesReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                SalesReportBtn.Background = new SolidColorBrush(Color.FromRgb(40, 167, 69));
                
                var sales = await App.DatabaseContext.Sales.ToListAsync();
                
                ReportContent.Children.Clear();
                
                // عنوان التقرير
                var title = new TextBlock
                {
                    Text = "📈 تقرير المبيعات التفصيلي",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                // إحصائيات سريعة
                var statsPanel = CreateStatsPanel(
                    $"إجمالي المبيعات: {sales.Count}",
                    $"إجمالي الإيرادات: {sales.Sum(s => s.FinalAmount):C}",
                    $"متوسط قيمة البيع: {(sales.Any() ? sales.Average(s => s.FinalAmount) : 0):C}",
                    $"المبيعات المدفوعة: {sales.Count(s => s.PaymentStatus == "مدفوع")}"
                );
                ReportContent.Children.Add(statsPanel);
                
                // جدول المبيعات
                var dataGrid = CreateSalesDataGrid(sales);
                ReportContent.Children.Add(dataGrid);
                
                // تحليل حالة الدفع
                var paymentAnalysis = CreatePaymentAnalysis(sales);
                ReportContent.Children.Add(paymentAnalysis);

                // تفعيل أزرار الطباعة والتصدير
                _hasReportContent = true;
                EnableExportButtons();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void InventoryReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                InventoryReportBtn.Background = new SolidColorBrush(Color.FromRgb(23, 162, 184));
                
                var inventory = await App.DatabaseContext.InventoryItems.ToListAsync();
                
                ReportContent.Children.Clear();
                
                var title = new TextBlock
                {
                    Text = "📦 تقرير المخزون التفصيلي",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                var statsPanel = CreateStatsPanel(
                    $"إجمالي العناصر: {inventory.Count}",
                    $"قيمة المخزون: {inventory.Sum(i => i.CurrentStock * i.UnitPrice):C}",
                    $"عناصر منخفضة: {inventory.Count(i => i.CurrentStock <= i.MinimumStock)}",
                    $"عناصر متاحة: {inventory.Count(i => i.Status == "متاح")}"
                );
                ReportContent.Children.Add(statsPanel);
                
                var dataGrid = CreateInventoryDataGrid(inventory);
                ReportContent.Children.Add(dataGrid);
                
                var lowStockAlert = CreateLowStockAlert(inventory);
                ReportContent.Children.Add(lowStockAlert);

                // تفعيل أزرار الطباعة والتصدير
                _hasReportContent = true;
                EnableExportButtons();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void CustomersReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                CustomersReportBtn.Background = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                
                var customers = await App.DatabaseContext.Customers.ToListAsync();
                var sales = await App.DatabaseContext.Sales.ToListAsync();
                
                ReportContent.Children.Clear();
                
                var title = new TextBlock
                {
                    Text = "👥 تقرير العملاء التفصيلي",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                var statsPanel = CreateStatsPanel(
                    $"إجمالي العملاء: {customers.Count}",
                    $"العملاء النشطون: {customers.Count(c => c.Status == "نشط")}",
                    $"إجمالي الحد الائتماني: {customers.Sum(c => c.CreditLimit):C}",
                    $"عملاء لديهم مبيعات: {sales.Select(s => s.CustomerName).Distinct().Count()}"
                );
                ReportContent.Children.Add(statsPanel);
                
                var dataGrid = CreateCustomersDataGrid(customers, sales);
                ReportContent.Children.Add(dataGrid);

                // تفعيل أزرار الطباعة والتصدير
                _hasReportContent = true;
                EnableExportButtons();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void MaintenanceReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                MaintenanceReportBtn.Background = new SolidColorBrush(Color.FromRgb(220, 53, 69));
                
                var maintenance = await App.DatabaseContext.MaintenanceRecords.ToListAsync();
                
                ReportContent.Children.Clear();
                
                var title = new TextBlock
                {
                    Text = "🛠️ تقرير الصيانة التفصيلي",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                var statsPanel = CreateStatsPanel(
                    $"إجمالي سجلات الصيانة: {maintenance.Count}",
                    $"إجمالي تكاليف الصيانة: {maintenance.Sum(m => m.Cost):C}",
                    $"صيانة مكتملة: {maintenance.Count(m => m.Status == "مكتملة")}",
                    $"صيانة مجدولة: {maintenance.Count(m => m.Status == "مجدولة")}"
                );
                ReportContent.Children.Add(statsPanel);
                
                var dataGrid = CreateMaintenanceDataGrid(maintenance);
                ReportContent.Children.Add(dataGrid);
                
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void FinancialReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                FinancialReportBtn.Background = new SolidColorBrush(Color.FromRgb(111, 66, 193));
                
                var sales = await App.DatabaseContext.Sales.ToListAsync();
                var maintenance = await App.DatabaseContext.MaintenanceRecords.ToListAsync();
                var inventory = await App.DatabaseContext.InventoryItems.ToListAsync();
                var devices = await App.DatabaseContext.MedicalDevices.ToListAsync();
                
                ReportContent.Children.Clear();
                
                var title = new TextBlock
                {
                    Text = "💰 التقرير المالي الشامل",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                var totalRevenue = sales.Sum(s => s.FinalAmount);
                var totalMaintenanceCost = maintenance.Sum(m => m.Cost);
                var inventoryValue = inventory.Sum(i => i.CurrentStock * i.UnitPrice);
                var assetsValue = devices.Sum(d => d.PurchasePrice);
                
                var statsPanel = CreateStatsPanel(
                    $"إجمالي الإيرادات: {totalRevenue:C}",
                    $"تكاليف الصيانة: {totalMaintenanceCost:C}",
                    $"قيمة المخزون: {inventoryValue:C}",
                    $"قيمة الأصول: {assetsValue:C}"
                );
                ReportContent.Children.Add(statsPanel);
                
                var profitLoss = CreateProfitLossStatement(totalRevenue, totalMaintenanceCost, inventoryValue);
                ReportContent.Children.Add(profitLoss);
                
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير المالي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void ComprehensiveReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ResetActiveButton();
                ComprehensiveReportBtn.Background = new SolidColorBrush(Color.FromRgb(52, 58, 64));
                
                ReportContent.Children.Clear();
                
                var title = new TextBlock
                {
                    Text = "📋 التقرير الشامل للنظام",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                ReportContent.Children.Add(title);
                
                // إحصائيات عامة
                var devices = await App.DatabaseContext.MedicalDevices.CountAsync();
                var inventory = await App.DatabaseContext.InventoryItems.CountAsync();
                var customers = await App.DatabaseContext.Customers.CountAsync();
                var suppliers = await App.DatabaseContext.Suppliers.CountAsync();
                var sales = await App.DatabaseContext.Sales.CountAsync();
                var shipments = await App.DatabaseContext.Shipments.CountAsync();
                var maintenance = await App.DatabaseContext.MaintenanceRecords.CountAsync();
                
                var overviewPanel = CreateOverviewPanel(devices, inventory, customers, suppliers, sales, shipments, maintenance);
                ReportContent.Children.Add(overviewPanel);
                
                // ملخص الأداء
                var performanceSummary = await CreatePerformanceSummary();
                ReportContent.Children.Add(performanceSummary);
                
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير الشامل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetActiveButton()
        {
            SalesReportBtn.Background = new SolidColorBrush(Color.FromRgb(40, 167, 69));
            InventoryReportBtn.Background = new SolidColorBrush(Color.FromRgb(23, 162, 184));
            CustomersReportBtn.Background = new SolidColorBrush(Color.FromRgb(255, 193, 7));
            MaintenanceReportBtn.Background = new SolidColorBrush(Color.FromRgb(220, 53, 69));
            FinancialReportBtn.Background = new SolidColorBrush(Color.FromRgb(111, 66, 193));
            ComprehensiveReportBtn.Background = new SolidColorBrush(Color.FromRgb(52, 58, 64));
        }

        private Border CreateStatsPanel(string stat1, string stat2, string stat3, string stat4)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            grid.RowDefinitions.Add(new RowDefinition());
            grid.RowDefinitions.Add(new RowDefinition());

            var stats = new[] { stat1, stat2, stat3, stat4 };
            var colors = new[]
            {
                Color.FromRgb(40, 167, 69),
                Color.FromRgb(23, 162, 184),
                Color.FromRgb(255, 193, 7),
                Color.FromRgb(220, 53, 69)
            };

            for (int i = 0; i < 4; i++)
            {
                var statBorder = new Border
                {
                    Background = new SolidColorBrush(colors[i]),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(15, 10, 15, 10),
                    Margin = new Thickness(5)
                };

                var statText = new TextBlock
                {
                    Text = stats[i],
                    Foreground = Brushes.White,
                    FontWeight = FontWeights.SemiBold,
                    FontSize = 14,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                statBorder.Child = statText;
                Grid.SetColumn(statBorder, i % 2);
                Grid.SetRow(statBorder, i / 2);
                grid.Children.Add(statBorder);
            }

            border.Child = grid;
            return border;
        }

        private DataGrid CreateSalesDataGrid(System.Collections.Generic.List<Sale> sales)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                IsReadOnly = true,
                Height = 300,
                Margin = new Thickness(0, 0, 0, 20),
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            dataGrid.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة", Binding = new System.Windows.Data.Binding("InvoiceNumber"), Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("SaleDate") { StringFormat = "dd/MM/yyyy" }, Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "العميل", Binding = new System.Windows.Data.Binding("CustomerName"), Width = 150 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الجهاز", Binding = new System.Windows.Data.Binding("DeviceName"), Width = 200 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "المبلغ النهائي", Binding = new System.Windows.Data.Binding("FinalAmount") { StringFormat = "C" }, Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "حالة الدفع", Binding = new System.Windows.Data.Binding("PaymentStatus"), Width = 100 });

            dataGrid.ItemsSource = sales;
            return dataGrid;
        }

        private Border CreatePaymentAnalysis(System.Collections.Generic.List<Sale> sales)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(255, 248, 225)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(255, 193, 7)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var stackPanel = new StackPanel();

            var title = new TextBlock
            {
                Text = "📊 تحليل حالة الدفع",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15)
            };
            stackPanel.Children.Add(title);

            var paymentGroups = sales.GroupBy(s => s.PaymentStatus).ToList();
            foreach (var group in paymentGroups)
            {
                var analysisText = new TextBlock
                {
                    Text = $"• {group.Key}: {group.Count()} مبيعة بقيمة {group.Sum(s => s.FinalAmount):C}",
                    FontSize = 14,
                    Margin = new Thickness(0, 5, 0, 5)
                };
                stackPanel.Children.Add(analysisText);
            }

            border.Child = stackPanel;
            return border;
        }

        private DataGrid CreateInventoryDataGrid(System.Collections.Generic.List<InventoryItem> inventory)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                IsReadOnly = true,
                Height = 300,
                Margin = new Thickness(0, 0, 0, 20),
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            dataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم العنصر", Binding = new System.Windows.Data.Binding("Name"), Width = 150 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الفئة", Binding = new System.Windows.Data.Binding("Category"), Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "المخزون الحالي", Binding = new System.Windows.Data.Binding("CurrentStock"), Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحد الأدنى", Binding = new System.Windows.Data.Binding("MinimumStock"), Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "سعر الوحدة", Binding = new System.Windows.Data.Binding("UnitPrice") { StringFormat = "C" }, Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 100 });

            dataGrid.ItemsSource = inventory;
            return dataGrid;
        }

        private Border CreateLowStockAlert(System.Collections.Generic.List<InventoryItem> inventory)
        {
            var lowStockItems = inventory.Where(i => i.CurrentStock <= i.MinimumStock).ToList();

            if (!lowStockItems.Any())
            {
                var successBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(212, 237, 218)),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(5),
                    Padding = new Thickness(20),
                    Margin = new Thickness(0, 0, 0, 20)
                };

                var successText = new TextBlock
                {
                    Text = "✅ جميع عناصر المخزون في المستوى الطبيعي",
                    FontSize = 16,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36))
                };

                successBorder.Child = successText;
                return successBorder;
            }

            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 215, 218)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var stackPanel = new StackPanel();

            var title = new TextBlock
            {
                Text = "⚠️ تنبيه: عناصر بمخزون منخفض",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36)),
                Margin = new Thickness(0, 0, 0, 15)
            };
            stackPanel.Children.Add(title);

            foreach (var item in lowStockItems)
            {
                var alertText = new TextBlock
                {
                    Text = $"• {item.Name}: {item.CurrentStock} {item.Unit} (الحد الأدنى: {item.MinimumStock})",
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36)),
                    Margin = new Thickness(0, 5, 0, 5)
                };
                stackPanel.Children.Add(alertText);
            }

            border.Child = stackPanel;
            return border;
        }

        private DataGrid CreateCustomersDataGrid(System.Collections.Generic.List<Customer> customers, System.Collections.Generic.List<Sale> sales)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                IsReadOnly = true,
                Height = 300,
                Margin = new Thickness(0, 0, 0, 20),
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            dataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم العميل", Binding = new System.Windows.Data.Binding("Name"), Width = 150 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "نوع العميل", Binding = new System.Windows.Data.Binding("CustomerType"), Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "المدينة", Binding = new System.Windows.Data.Binding("City"), Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحد الائتماني", Binding = new System.Windows.Data.Binding("CreditLimit") { StringFormat = "C" }, Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 80 });

            dataGrid.ItemsSource = customers;
            return dataGrid;
        }

        private DataGrid CreateMaintenanceDataGrid(System.Collections.Generic.List<MaintenanceRecord> maintenance)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                IsReadOnly = true,
                Height = 300,
                Margin = new Thickness(0, 0, 0, 20),
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromRgb(248, 249, 250))
            };

            dataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم الجهاز", Binding = new System.Windows.Data.Binding("DeviceName"), Width = 180 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "نوع الصيانة", Binding = new System.Windows.Data.Binding("MaintenanceType"), Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الصيانة", Binding = new System.Windows.Data.Binding("MaintenanceDate") { StringFormat = "dd/MM/yyyy" }, Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "التكلفة", Binding = new System.Windows.Data.Binding("Cost") { StringFormat = "C" }, Width = 100 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "اسم الفني", Binding = new System.Windows.Data.Binding("TechnicianName"), Width = 120 });

            dataGrid.ItemsSource = maintenance;
            return dataGrid;
        }

        private Border CreateProfitLossStatement(decimal totalRevenue, decimal totalMaintenanceCost, decimal inventoryValue)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(230, 244, 241)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(111, 66, 193)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var stackPanel = new StackPanel();

            var title = new TextBlock
            {
                Text = "📊 بيان الأرباح والخسائر",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15)
            };
            stackPanel.Children.Add(title);

            var netProfit = totalRevenue - totalMaintenanceCost;
            var profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

            var items = new[]
            {
                $"إجمالي الإيرادات: {totalRevenue:C}",
                $"تكاليف الصيانة: {totalMaintenanceCost:C}",
                $"صافي الربح: {netProfit:C}",
                $"هامش الربح: {profitMargin:F1}%",
                $"قيمة الأصول (المخزون): {inventoryValue:C}"
            };

            foreach (var item in items)
            {
                var itemText = new TextBlock
                {
                    Text = $"• {item}",
                    FontSize = 14,
                    Margin = new Thickness(0, 5, 0, 5)
                };
                stackPanel.Children.Add(itemText);
            }

            border.Child = stackPanel;
            return border;
        }

        private Border CreateOverviewPanel(int devices, int inventory, int customers, int suppliers, int sales, int shipments, int maintenance)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var stackPanel = new StackPanel();

            var title = new TextBlock
            {
                Text = "📊 نظرة عامة على النظام",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15)
            };
            stackPanel.Children.Add(title);

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            grid.ColumnDefinitions.Add(new ColumnDefinition());
            grid.ColumnDefinitions.Add(new ColumnDefinition());

            var stats = new[]
            {
                ($"🏥 الأجهزة الطبية", devices.ToString()),
                ($"📦 عناصر المخزون", inventory.ToString()),
                ($"👥 العملاء", customers.ToString()),
                ($"🏭 الموردين", suppliers.ToString()),
                ($"💰 المبيعات", sales.ToString()),
                ($"🚚 الشحنات", shipments.ToString()),
                ($"🛠️ سجلات الصيانة", maintenance.ToString())
            };

            for (int i = 0; i < stats.Length; i++)
            {
                var statPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(10, 5, 10, 5)
                };

                var label = new TextBlock
                {
                    Text = stats[i].Item1 + ": ",
                    FontWeight = FontWeights.SemiBold,
                    Width = 120
                };

                var value = new TextBlock
                {
                    Text = stats[i].Item2,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69))
                };

                statPanel.Children.Add(label);
                statPanel.Children.Add(value);

                Grid.SetColumn(statPanel, i % 3);
                Grid.SetRow(statPanel, i / 3);

                if (i / 3 >= grid.RowDefinitions.Count)
                    grid.RowDefinitions.Add(new RowDefinition());

                grid.Children.Add(statPanel);
            }

            stackPanel.Children.Add(grid);
            border.Child = stackPanel;
            return border;
        }

        private async System.Threading.Tasks.Task<Border> CreatePerformanceSummary()
        {
            var sales = await App.DatabaseContext.Sales.ToListAsync();
            var devices = await App.DatabaseContext.MedicalDevices.ToListAsync();
            var inventory = await App.DatabaseContext.InventoryItems.ToListAsync();

            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(255, 248, 225)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(255, 193, 7)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var stackPanel = new StackPanel();

            var title = new TextBlock
            {
                Text = "📈 ملخص الأداء",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 15)
            };
            stackPanel.Children.Add(title);

            var totalRevenue = sales.Sum(s => s.FinalAmount);
            var averageSale = sales.Any() ? sales.Average(s => s.FinalAmount) : 0;
            var lowStockCount = inventory.Count(i => i.CurrentStock <= i.MinimumStock);
            var availableDevices = devices.Count(d => d.Status == "متاح");

            var performanceItems = new[]
            {
                $"إجمالي الإيرادات: {totalRevenue:C}",
                $"متوسط قيمة البيع: {averageSale:C}",
                $"الأجهزة المتاحة: {availableDevices} من {devices.Count}",
                $"عناصر بمخزون منخفض: {lowStockCount}",
                $"معدل النجاح: {(availableDevices * 100.0 / Math.Max(devices.Count, 1)):F1}%"
            };

            foreach (var item in performanceItems)
            {
                var itemText = new TextBlock
                {
                    Text = $"• {item}",
                    FontSize = 14,
                    Margin = new Thickness(0, 5, 0, 5)
                };
                stackPanel.Children.Add(itemText);
            }

            border.Child = stackPanel;
            return border;
        }

        // دوال الطباعة والتصدير
        private void EnableExportButtons()
        {
            PrintReportBtn.IsEnabled = _hasReportContent;
            ExportPdfBtn.IsEnabled = _hasReportContent;
            ExportExcelBtn.IsEnabled = _hasReportContent;
        }

        private void PrintReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreatePrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "التقرير المتقدم");
                    MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportPdfBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_متقدم_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    MessageBox.Show("ميزة تصدير PDF قيد التطوير - سيتم إضافتها قريباً", "قيد التطوير",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportExcelBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_متقدم_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    MessageBox.Show("ميزة تصدير Excel قيد التطوير - سيتم إضافتها قريباً", "قيد التطوير",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreatePrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // العنوان
            var title = new Paragraph(new Run("التقرير المتقدم"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // تاريخ التقرير
            var dateInfo = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // محتوى التقرير
            var content = new Paragraph(new Run("محتوى التقرير سيتم إضافته هنا حسب نوع التقرير المحدد"))
            {
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(content);

            return document;
        }
    }
}
