using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Services
{
    public class SmartAlertService
    {
        private readonly MedicalDevicesContext _context;

        public SmartAlertService(MedicalDevicesContext context)
        {
            _context = context;
        }

        // إنشاء تنبيهات الضمان
        public async Task GenerateWarrantyAlertsAsync()
        {
            try
            {
                var installations = await _context.DeviceInstallations
                    .Where(i => i.IsActive && i.WarrantyEndDate > DateTime.Now)
                    .ToListAsync();

                foreach (var installation in installations)
                {
                    var daysUntilExpiry = (installation.WarrantyEndDate - DateTime.Now).Days;

                    // تنبيه قبل 30 يوم من انتهاء الضمان
                    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0)
                    {
                        await CreateAlertIfNotExists(
                            "ضمان",
                            $"انتهاء ضمان الجهاز قريباً",
                            $"سينتهي ضمان الجهاز '{installation.DeviceName}' للعميل '{installation.CustomerName}' خلال {daysUntilExpiry} يوم",
                            daysUntilExpiry <= 7 ? "عالي" : "متوسط",
                            installation.Id,
                            "Installation",
                            installation.DeviceName,
                            installation.WarrantyEndDate,
                            "تجديد الضمان أو التواصل مع العميل"
                        );
                    }

                    // تنبيه بعد انتهاء الضمان
                    if (daysUntilExpiry <= 0)
                    {
                        await CreateAlertIfNotExists(
                            "ضمان",
                            $"انتهى ضمان الجهاز",
                            $"انتهى ضمان الجهاز '{installation.DeviceName}' للعميل '{installation.CustomerName}' منذ {Math.Abs(daysUntilExpiry)} يوم",
                            "عالي",
                            installation.Id,
                            "Installation",
                            installation.DeviceName,
                            DateTime.Now,
                            "متابعة حالة الجهاز وتقديم خدمات ما بعد الضمان"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء تنبيهات الضمان: {ex.Message}");
            }
        }

        // إنشاء تنبيهات الصيانة
        public async Task GenerateMaintenanceAlertsAsync()
        {
            try
            {
                var maintenanceRecords = await _context.MaintenanceRecords
                    .Where(m => m.Status == "مجدولة" && m.MaintenanceDate >= DateTime.Now)
                    .ToListAsync();

                foreach (var maintenance in maintenanceRecords)
                {
                    var daysUntilMaintenance = (maintenance.MaintenanceDate - DateTime.Now).Days;

                    // تنبيه قبل 7 أيام من الصيانة المجدولة
                    if (daysUntilMaintenance <= 7 && daysUntilMaintenance >= 0)
                    {
                        await CreateAlertIfNotExists(
                            "صيانة",
                            $"صيانة مجدولة قريباً",
                            $"صيانة مجدولة للجهاز '{maintenance.DeviceName}' خلال {daysUntilMaintenance} يوم",
                            daysUntilMaintenance <= 2 ? "عالي" : "متوسط",
                            maintenance.Id,
                            "Maintenance",
                            maintenance.DeviceName,
                            maintenance.MaintenanceDate,
                            "تحضير المعدات والتواصل مع الفني"
                        );
                    }
                }

                // تنبيهات الصيانة المتأخرة
                var overdueMaintenance = await _context.MaintenanceRecords
                    .Where(m => m.Status == "مجدولة" && m.MaintenanceDate < DateTime.Now)
                    .ToListAsync();

                foreach (var maintenance in overdueMaintenance)
                {
                    var daysOverdue = (DateTime.Now - maintenance.MaintenanceDate).Days;

                    await CreateAlertIfNotExists(
                        "صيانة",
                        $"صيانة متأخرة",
                        $"الصيانة المجدولة للجهاز '{maintenance.DeviceName}' متأخرة بـ {daysOverdue} يوم",
                        "عالي",
                        maintenance.Id,
                        "Maintenance",
                        maintenance.DeviceName,
                        DateTime.Now,
                        "تنفيذ الصيانة المتأخرة فوراً"
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء تنبيهات الصيانة: {ex.Message}");
            }
        }

        // إنشاء تنبيهات المخزون
        public async Task GenerateInventoryAlertsAsync()
        {
            try
            {
                var lowStockItems = await _context.InventoryItems
                    .Where(i => i.CurrentStock <= i.MinimumStock && i.Status == "متاح")
                    .ToListAsync();

                foreach (var item in lowStockItems)
                {
                    await CreateAlertIfNotExists(
                        "مخزون",
                        $"مخزون منخفض",
                        $"المخزون منخفض للصنف '{item.Name}' - الكمية الحالية: {item.CurrentStock}, الحد الأدنى: {item.MinimumStock}",
                        item.CurrentStock == 0 ? "عالي" : "متوسط",
                        item.Id,
                        "Inventory",
                        item.Name,
                        DateTime.Now,
                        "طلب كمية جديدة من المورد"
                    );
                }

                // تنبيهات انتهاء الصلاحية
                var expiringItems = await _context.InventoryItems
                    .Where(i => i.ExpiryDate.HasValue && i.ExpiryDate <= DateTime.Now.AddDays(30) && i.Status == "متاح")
                    .ToListAsync();

                foreach (var item in expiringItems)
                {
                    var daysUntilExpiry = (item.ExpiryDate.Value - DateTime.Now).Days;

                    await CreateAlertIfNotExists(
                        "مخزون",
                        $"انتهاء صلاحية قريب",
                        $"ستنتهي صلاحية الصنف '{item.Name}' خلال {daysUntilExpiry} يوم",
                        daysUntilExpiry <= 7 ? "عالي" : "متوسط",
                        item.Id,
                        "Inventory",
                        item.Name,
                        item.ExpiryDate,
                        "استخدام الصنف أو التخلص منه قبل انتهاء الصلاحية"
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء تنبيهات المخزون: {ex.Message}");
            }
        }

        // إنشاء تنبيه إذا لم يكن موجوداً
        private async Task CreateAlertIfNotExists(string alertType, string title, string message, 
            string priority, int? relatedItemId, string relatedItemType, string relatedItemName, 
            DateTime? dueDate, string actionRequired)
        {
            try
            {
                // التحقق من وجود تنبيه مشابه
                var existingAlert = await _context.SmartAlerts
                    .FirstOrDefaultAsync(a => a.AlertType == alertType && 
                                            a.RelatedItemId == relatedItemId && 
                                            a.RelatedItemType == relatedItemType && 
                                            a.Status == "نشط");

                if (existingAlert == null)
                {
                    var newAlert = new SmartAlert
                    {
                        AlertType = alertType,
                        Title = title,
                        Message = message,
                        Priority = priority,
                        Status = "نشط",
                        RelatedItemId = relatedItemId,
                        RelatedItemType = relatedItemType,
                        RelatedItemName = relatedItemName,
                        AlertDate = DateTime.Now,
                        DueDate = dueDate,
                        ActionRequired = actionRequired,
                        IsAutoGenerated = true,
                        Category = alertType,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    _context.SmartAlerts.Add(newAlert);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    // تحديث التنبيه الموجود
                    existingAlert.Message = message;
                    existingAlert.Priority = priority;
                    existingAlert.DueDate = dueDate;
                    existingAlert.LastUpdated = DateTime.Now;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التنبيه: {ex.Message}");
            }
        }

        // تشغيل جميع التنبيهات
        public async Task GenerateAllAlertsAsync()
        {
            await GenerateWarrantyAlertsAsync();
            await GenerateMaintenanceAlertsAsync();
            await GenerateInventoryAlertsAsync();
        }

        // الحصول على التنبيهات النشطة
        public async Task<List<SmartAlert>> GetActiveAlertsAsync()
        {
            return await _context.SmartAlerts
                .Where(a => a.IsActive && a.Status == "نشط")
                .OrderByDescending(a => a.Priority == "عالي" ? 3 : a.Priority == "متوسط" ? 2 : 1)
                .ThenByDescending(a => a.CreatedDate)
                .ToListAsync();
        }

        // تحديث حالة التنبيه
        public async Task UpdateAlertStatusAsync(int alertId, string newStatus)
        {
            var alert = await _context.SmartAlerts.FindAsync(alertId);
            if (alert != null)
            {
                alert.Status = newStatus;
                alert.LastUpdated = DateTime.Now;
                
                if (newStatus == "مكتمل")
                {
                    alert.CompletedDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();
            }
        }

        // حذف التنبيهات القديمة
        public async Task CleanupOldAlertsAsync()
        {
            var oldAlerts = await _context.SmartAlerts
                .Where(a => a.CreatedDate < DateTime.Now.AddDays(-30) && 
                           (a.Status == "مكتمل" || a.Status == "مقروء"))
                .ToListAsync();

            _context.SmartAlerts.RemoveRange(oldAlerts);
            await _context.SaveChangesAsync();
        }
    }
}
