<Window x:Class="MedicalDevicesManager.Windows.AddEditSaleWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل مبيعة" Height="700" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة مبيعة جديدة" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- رقم الفاتورة -->
                <TextBlock Text="رقم الفاتورة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="InvoiceNumberTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15" IsReadOnly="True"/>
                
                <!-- تاريخ البيع -->
                <TextBlock Text="تاريخ البيع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="SaleDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- العميل -->
                <TextBlock Text="العميل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CustomerComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15" 
                          DisplayMemberPath="Name" SelectedValuePath="Id"/>
                
                <!-- الجهاز الطبي -->
                <TextBlock Text="الجهاز الطبي *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0" x:Name="DeviceComboBox" Height="35" Padding="10,8" FontSize="14"
                              DisplayMemberPath="Name" SelectedValuePath="Id" SelectionChanged="DeviceComboBox_SelectionChanged"
                              Margin="0,0,5,0"/>

                    <Button Grid.Column="1" x:Name="SelectFromInventoryBtn" Content="📦" Width="35" Height="35"
                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                            ToolTip="اختيار من المخزون" Click="SelectFromInventoryBtn_Click"/>
                </Grid>
                
                <!-- الكمية وسعر الوحدة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الكمية *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="QuantityTextBox" Height="35" Padding="10,8" FontSize="14" TextChanged="CalculateTotal"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="سعر الوحدة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="UnitPriceTextBox" Height="35" Padding="10,8" FontSize="14" TextChanged="CalculateTotal"/>
                    </StackPanel>
                </Grid>
                
                <!-- المبلغ الإجمالي والخصم -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المبلغ الإجمالي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TotalAmountTextBox" Height="35" Padding="10,8" FontSize="14" IsReadOnly="True" Background="#F8F9FA"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الخصم (%)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" x:Name="DiscountPercentageTextBox" Height="35" Padding="10,8" FontSize="14"
                                     TextChanged="CalculateTotal" Text="0"/>
                            <TextBlock Grid.Column="1" Text="%" VerticalAlignment="Center" Margin="5,0,0,0" FontWeight="Bold"/>
                        </Grid>
                    </StackPanel>
                </Grid>
                
                <!-- المبلغ النهائي -->
                <TextBlock Text="المبلغ النهائي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="FinalAmountTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15" 
                         IsReadOnly="True" Background="#F8F9FA" FontWeight="Bold"/>
                
                <!-- حالة الدفع -->
                <TextBlock Text="حالة الدفع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="PaymentStatusComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"
                          SelectionChanged="PaymentStatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="مدفوع"/>
                    <ComboBoxItem Content="مؤجل"/>
                    <ComboBoxItem Content="دفع جزئي"/>
                    <ComboBoxItem Content="ملغي"/>
                </ComboBox>

                <!-- نسبة الدفع الجزئي -->
                <Border x:Name="PartialPaymentPanel" Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1"
                        CornerRadius="5" Padding="15" Margin="0,0,0,15" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="💰 تفاصيل الدفع الجزئي" FontWeight="Bold" Margin="0,0,0,10" Foreground="#856404"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="المبلغ المدفوع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox x:Name="PaidAmountTextBox" Height="30" Padding="8" FontSize="12"
                                         TextChanged="PaidAmountTextBox_TextChanged"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="نسبة الدفع (%)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox x:Name="PaymentPercentageTextBox" Height="30" Padding="8" FontSize="12"
                                         IsReadOnly="True" Background="#F8F9FA" FontWeight="Bold"/>
                            </StackPanel>
                        </Grid>

                        <TextBlock x:Name="RemainingAmountTextBlock" Text="المبلغ المتبقي: 0 ر.س"
                                   FontWeight="Bold" Margin="0,10,0,0" Foreground="#DC3545"/>
                    </StackPanel>
                </Border>
                
                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="60" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- معلومات إضافية -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات إضافية" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <TextBlock x:Name="DeviceInfoTextBlock" Text="اختر جهازاً لعرض المعلومات" FontSize="12" Foreground="#6C757D"/>
                        <TextBlock x:Name="CustomerInfoTextBlock" Text="اختر عميلاً لعرض المعلومات" FontSize="12" Foreground="#6C757D" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ المبيعة" Width="140" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
