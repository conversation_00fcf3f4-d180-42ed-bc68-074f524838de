<Window x:Class="MedicalDevicesManager.Windows.AddEditInstallationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة تنصيب الأجهزة الطبية" Height="700" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,0,0,20">
            <TextBlock x:Name="TitleTextBlock" Text="🔧 إضافة تنصيب جديد" 
                       FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                       Foreground="#1976D2"/>
        </Border>
        
        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- معلومات الجهاز -->
                <GroupBox Grid.Row="0" Header="معلومات الجهاز" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="الجهاز والرقم التسلسلي:" FontWeight="Bold"
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <Grid Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="SelectedDeviceTextBox" Height="30"
                                     IsReadOnly="True" Background="#F5F5F5"
                                     Text="لم يتم اختيار جهاز" VerticalContentAlignment="Center"
                                     Margin="0,0,10,0"/>
                            <Button Grid.Column="1" x:Name="SelectDeviceButton" Content="🔍 اختيار جهاز"
                                    Width="120" Height="30" Background="#2196F3" Foreground="White"
                                    BorderThickness="0" FontSize="12" FontWeight="SemiBold"
                                    Click="SelectDeviceButton_Click"/>
                        </Grid>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الموديل:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="ModelTextBox" Height="30"
                                 Margin="0,0,20,10" IsReadOnly="False" Background="White"
                                 ToolTip="يمكنك تعديل الموديل حسب الحاجة"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="الشركة المصنعة:" FontWeight="Bold"
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="3" x:Name="ManufacturerTextBox" Height="30"
                                 Margin="0,0,0,10" IsReadOnly="False" Background="White"
                                 ToolTip="يمكنك تعديل الشركة المصنعة حسب الحاجة"/>
                    </Grid>
                </GroupBox>
                
                <!-- معلومات العميل -->
                <GroupBox Grid.Row="1" Header="معلومات العميل" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="العميل:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" x:Name="CustomerComboBox" Height="30" 
                                  Margin="0,0,0,10"/>
                    </Grid>
                </GroupBox>
                
                <!-- معلومات التنصيب -->
                <GroupBox Grid.Row="2" Header="معلومات التنصيب" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ التنصيب:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <DatePicker Grid.Row="0" Grid.Column="1" x:Name="InstallationDatePicker" Height="30" 
                                    Margin="0,0,20,10"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="عدد سنوات الضمان:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="0" Grid.Column="3" x:Name="WarrantyYearsTextBox" Height="30" 
                                 Margin="0,0,0,10" TextChanged="WarrantyYearsTextBox_TextChanged"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ انتهاء الضمان:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <DatePicker Grid.Row="1" Grid.Column="1" x:Name="WarrantyEndDatePicker" Height="30"
                                    Margin="0,0,20,10" SelectedDateChanged="WarrantyEndDatePicker_SelectedDateChanged"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="تكلفة التنصيب:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="3" x:Name="InstallationCostTextBox" Height="30" 
                                 Margin="0,0,0,10"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="موقع التنصيب:" FontWeight="Bold" 
                                   VerticalAlignment="Top" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" x:Name="InstallationLocationTextBox" 
                                 Height="60" Margin="0,0,0,10" TextWrapping="Wrap" AcceptsReturn="True" 
                                 VerticalScrollBarVisibility="Auto"/>
                    </Grid>
                </GroupBox>
                
                <!-- معلومات الاتصال -->
                <GroupBox Grid.Row="3" Header="معلومات الاتصال بالمسؤول" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المسؤول:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="ContactPersonNameTextBox" Height="30" 
                                 Margin="0,0,20,10"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="رقم الهاتف:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="0" Grid.Column="3" x:Name="ContactPersonPhoneTextBox" Height="30" 
                                 Margin="0,0,0,10"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="البريد الإلكتروني:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="ContactPersonEmailTextBox" Height="30" 
                                 Margin="0,0,20,10"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="المنصب:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="3" x:Name="ContactPersonPositionTextBox" Height="30" 
                                 Margin="0,0,0,10"/>
                    </Grid>
                </GroupBox>
                
                <!-- معلومات إضافية -->
                <GroupBox Grid.Row="4" Header="معلومات إضافية" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="حالة التنصيب:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" x:Name="InstallationStatusComboBox" Height="30" 
                                  Margin="0,0,20,10">
                            <ComboBoxItem Content="مكتمل"/>
                            <ComboBoxItem Content="قيد التنصيب"/>
                            <ComboBoxItem Content="مؤجل"/>
                            <ComboBoxItem Content="ملغي"/>
                        </ComboBox>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="اسم الفني:" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="0" Grid.Column="3" x:Name="TechnicianNameTextBox" Height="30"
                                 Margin="0,0,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="حالة المعدات:" FontWeight="Bold"
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" x:Name="EquipmentConditionComboBox" Height="30"
                                  Margin="0,0,20,10">
                            <ComboBoxItem Content="ممتاز"/>
                            <ComboBoxItem Content="جيد"/>
                            <ComboBoxItem Content="مقبول"/>
                            <ComboBoxItem Content="يحتاج صيانة"/>
                        </ComboBox>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="الشركة المجهزة:" FontWeight="Bold"
                                   VerticalAlignment="Center" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="1" Grid.Column="3" x:Name="SupplierCompanyTextBox" Height="30"
                                 Margin="0,0,0,10"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="ملاحظات التنصيب:" FontWeight="Bold"
                                   VerticalAlignment="Top" Margin="0,0,15,10"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" x:Name="InstallationNotesTextBox"
                                 Height="80" Margin="0,0,0,10" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </Grid>
                </GroupBox>


            </Grid>
        </ScrollViewer>

        <!-- كتيب تقرير التنصيب -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                CornerRadius="8" Margin="0,15,0,15" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عنوان القسم -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📄" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="كتيب تقرير التنصيب" FontSize="16" FontWeight="Bold"
                               Foreground="#495057" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- أدوات إدارة الملف -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0" x:Name="InstallationReportPathTextBox" Height="40" Padding="12,10"
                             FontSize="14" IsReadOnly="True" Background="White" BorderBrush="#CED4DA"
                             Text="لم يتم اختيار ملف" Margin="0,0,10,0" VerticalContentAlignment="Center"/>

                    <Button Grid.Column="1" x:Name="BrowseInstallationReportBtn" Content="📁 استعراض" Width="110" Height="40"
                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                            FontSize="13" FontWeight="SemiBold" Margin="0,0,10,0" Click="BrowseInstallationReportBtn_Click"/>

                    <Button Grid.Column="2" x:Name="ViewInstallationReportBtn" Content="👁️ عرض" Width="90" Height="40"
                            Background="#28A745" Foreground="White" BorderThickness="0"
                            FontSize="13" FontWeight="SemiBold" Margin="0,0,10,0" Click="ViewInstallationReportBtn_Click"
                            IsEnabled="False"/>

                    <Button Grid.Column="3" x:Name="RemoveInstallationReportBtn" Content="🗑️ حذف" Width="90" Height="40"
                            Background="#DC3545" Foreground="White" BorderThickness="0"
                            FontSize="13" FontWeight="SemiBold" Click="RemoveInstallationReportBtn_Click"
                            IsEnabled="False"/>
                </Grid>
            </Grid>
        </Border>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="3" Margin="0,0,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="1" x:Name="SaveButton" Content="💾 حفظ" Width="100" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            
            <Button Grid.Column="2" x:Name="CancelButton" Content="❌ إلغاء" Width="100" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </Grid>
    </Grid>
</Window>
