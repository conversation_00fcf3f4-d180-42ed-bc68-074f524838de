using System;
using System.IO;
using System.Windows;
using System.Diagnostics;

namespace MedicalDevicesManager.Windows
{
    public partial class MaintenanceDetailsWindow : Window
    {
        private MaintenanceRecord _maintenance;
        
        public MaintenanceDetailsWindow(MaintenanceRecord maintenance)
        {
            InitializeComponent();
            _maintenance = maintenance;
            LoadMaintenanceDetails();
        }
        
        private void LoadMaintenanceDetails()
        {
            try
            {
                if (_maintenance == null) return;
                
                // معلومات الجهاز
                DeviceNameTextBlock.Text = _maintenance.DeviceName ?? "غير محدد";
                SerialNumberTextBlock.Text = _maintenance.SerialNumber ?? "غير محدد";
                MaintenanceTypeTextBlock.Text = _maintenance.MaintenanceType ?? "غير محدد";
                StatusTextBlock.Text = _maintenance.Status ?? "غير محدد";
                
                // معلومات الصيانة
                MaintenanceDateTextBlock.Text = _maintenance.MaintenanceDate.ToString("dd/MM/yyyy");
                NextMaintenanceDateTextBlock.Text = _maintenance.NextMaintenanceDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
                TechnicianNameTextBlock.Text = _maintenance.TechnicianName ?? "غير محدد";
                SupplierCompanyTextBlock.Text = _maintenance.SupplierCompany ?? "غير محدد";
                CostTextBlock.Text = _maintenance.Cost.ToString("C");
                CreatedDateTextBlock.Text = _maintenance.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = _maintenance.LastUpdated.ToString("dd/MM/yyyy HH:mm");
                
                // الوصف والملاحظات
                DescriptionTextBlock.Text = string.IsNullOrEmpty(_maintenance.Description) ? "لا يوجد وصف" : _maintenance.Description;
                NotesTextBlock.Text = string.IsNullOrEmpty(_maintenance.Notes) ? "لا توجد ملاحظات" : _maintenance.Notes;
                
                // كتيب تقرير الصيانة
                if (!string.IsNullOrEmpty(_maintenance.ReportBookletPath) && File.Exists(_maintenance.ReportBookletPath))
                {
                    ReportBookletTextBlock.Text = Path.GetFileName(_maintenance.ReportBookletPath);
                    ViewReportBtn.IsEnabled = true;
                }
                else
                {
                    ReportBookletTextBlock.Text = "لا يوجد كتيب مرفق";
                    ViewReportBtn.IsEnabled = false;
                }
                
                // الأوراق والمخاطبات
                if (!string.IsNullOrEmpty(_maintenance.DocumentsPath) && File.Exists(_maintenance.DocumentsPath))
                {
                    DocumentsTextBlock.Text = Path.GetFileName(_maintenance.DocumentsPath);
                    ViewDocumentsBtn.IsEnabled = true;
                }
                else
                {
                    DocumentsTextBlock.Text = "لا توجد أوراق مرفقة";
                    ViewDocumentsBtn.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ViewReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_maintenance.ReportBookletPath) && File.Exists(_maintenance.ReportBookletPath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = _maintenance.ReportBookletPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ViewDocumentsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_maintenance.DocumentsPath) && File.Exists(_maintenance.DocumentsPath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = _maintenance.DocumentsPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new AddEditMaintenanceWindow(_maintenance);
                if (editWindow.ShowDialog() == true)
                {
                    // تحديث البيانات المعروضة
                    LoadMaintenanceDetails();
                    MessageBox.Show("تم تحديث سجل الصيانة بنجاح!", "نجح التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
