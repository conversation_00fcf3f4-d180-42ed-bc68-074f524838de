﻿#pragma checksum "..\..\..\..\..\Windows\NotificationsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B6F58E34147F31A0EA276005FF51B65472B1C63F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// NotificationsWindow
    /// </summary>
    public partial class NotificationsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 31 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkAllReadBtn;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckSystemBtn;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid NotificationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotificationDetailsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewRelatedItemBtn;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnreadCountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/notificationswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MarkAllReadBtn = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.MarkAllReadBtn.Click += new System.Windows.RoutedEventHandler(this.MarkAllReadBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CheckSystemBtn = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.CheckSystemBtn.Click += new System.Windows.RoutedEventHandler(this.CheckSystemBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.FilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 45 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.FilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.NotificationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 61 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.NotificationsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NotificationsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NotificationDetailsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.NotificationDetailsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ViewRelatedItemBtn = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            this.ViewRelatedItemBtn.Click += new System.Windows.RoutedEventHandler(this.ViewRelatedItemBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.UnreadCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 6:
            
            #line 130 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkReadBtn_Click);
            
            #line default
            #line hidden
            break;
            case 7:
            
            #line 134 "..\..\..\..\..\Windows\NotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

