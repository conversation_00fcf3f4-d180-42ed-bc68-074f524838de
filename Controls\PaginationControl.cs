using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// عنصر تحكم متقدم للتنقل بين الصفحات
    /// </summary>
    public class PaginationControl : UserControl
    {
        #region Properties

        public static readonly DependencyProperty CurrentPageProperty =
            DependencyProperty.Register("CurrentPage", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(1, OnCurrentPageChanged));

        public static readonly DependencyProperty TotalPagesProperty =
            DependencyProperty.Register("TotalPages", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(1, OnTotalPagesChanged));

        public static readonly DependencyProperty PageSizeProperty =
            DependencyProperty.Register("PageSize", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(50, OnPageSizeChanged));

        public static readonly DependencyProperty TotalRecordsProperty =
            DependencyProperty.Register("TotalRecords", typeof(int), typeof(PaginationControl), 
                new PropertyMetadata(0, OnTotalRecordsChanged));

        public int CurrentPage
        {
            get { return (int)GetValue(CurrentPageProperty); }
            set { SetValue(CurrentPageProperty, value); }
        }

        public int TotalPages
        {
            get { return (int)GetValue(TotalPagesProperty); }
            set { SetValue(TotalPagesProperty, value); }
        }

        public int PageSize
        {
            get { return (int)GetValue(PageSizeProperty); }
            set { SetValue(PageSizeProperty, value); }
        }

        public int TotalRecords
        {
            get { return (int)GetValue(TotalRecordsProperty); }
            set { SetValue(TotalRecordsProperty, value); }
        }

        // عناصر التحكم
        private Button _firstPageButton;
        private Button _previousPageButton;
        private Button _nextPageButton;
        private Button _lastPageButton;
        private TextBox _currentPageTextBox;
        private TextBlock _totalPagesTextBlock;
        private ComboBox _pageSizeComboBox;
        private TextBlock _recordsInfoTextBlock;
        private StackPanel _pageNumbersPanel;

        // إعدادات العرض
        public bool ShowPageNumbers { get; set; } = true;
        public bool ShowPageSizeSelector { get; set; } = true;
        public bool ShowRecordsInfo { get; set; } = true;
        public bool ShowNavigationButtons { get; set; } = true;
        public int MaxVisiblePageNumbers { get; set; } = 10;

        #endregion

        #region Events

        public event EventHandler<PageChangedEventArgs> PageChanged;
        public event EventHandler<PageSizeChangedEventArgs> PageSizeChanged;

        #endregion

        #region Constructor

        public PaginationControl()
        {
            InitializeComponent();
            UpdateControls();
        }

        #endregion

        #region Initialization

        private void InitializeComponent()
        {
            var mainPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                Height = 50
            };

            // معلومات السجلات
            if (ShowRecordsInfo)
            {
                _recordsInfoTextBlock = new TextBlock
                {
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 20, 0),
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
                };
                mainPanel.Children.Add(_recordsInfoTextBlock);
            }

            // أزرار التنقل
            if (ShowNavigationButtons)
            {
                _firstPageButton = CreateNavigationButton("⏮️", "الصفحة الأولى", FirstPage_Click);
                _previousPageButton = CreateNavigationButton("◀️", "الصفحة السابقة", PreviousPage_Click);

                mainPanel.Children.Add(_firstPageButton);
                mainPanel.Children.Add(_previousPageButton);
            }

            // أرقام الصفحات
            if (ShowPageNumbers)
            {
                _pageNumbersPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 10, 0)
                };
                mainPanel.Children.Add(_pageNumbersPanel);
            }
            else
            {
                // مربع الصفحة الحالية
                _currentPageTextBox = new TextBox
                {
                    Width = 50,
                    Height = 30,
                    TextAlignment = TextAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(5, 0, 5, 0)
                };
                _currentPageTextBox.KeyDown += CurrentPageTextBox_KeyDown;

                var ofLabel = new TextBlock
                {
                    Text = " من ",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(5, 0, 5, 0)
                };

                _totalPagesTextBlock = new TextBlock
                {
                    VerticalAlignment = VerticalAlignment.Center,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 10, 0)
                };

                mainPanel.Children.Add(_currentPageTextBox);
                mainPanel.Children.Add(ofLabel);
                mainPanel.Children.Add(_totalPagesTextBlock);
            }

            // أزرار التنقل
            if (ShowNavigationButtons)
            {
                _nextPageButton = CreateNavigationButton("▶️", "الصفحة التالية", NextPage_Click);
                _lastPageButton = CreateNavigationButton("⏭️", "الصفحة الأخيرة", LastPage_Click);

                mainPanel.Children.Add(_nextPageButton);
                mainPanel.Children.Add(_lastPageButton);
            }

            // اختيار حجم الصفحة
            if (ShowPageSizeSelector)
            {
                var pageSizeLabel = new TextBlock
                {
                    Text = " عدد السجلات: ",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(20, 0, 5, 0)
                };

                _pageSizeComboBox = new ComboBox
                {
                    Width = 80,
                    Height = 30,
                    VerticalAlignment = VerticalAlignment.Center
                };

                // إضافة خيارات حجم الصفحة
                var pageSizes = new[] { 10, 25, 50, 100, 200, 500 };
                foreach (var size in pageSizes)
                {
                    _pageSizeComboBox.Items.Add(size);
                }
                _pageSizeComboBox.SelectedItem = PageSize;
                _pageSizeComboBox.SelectionChanged += PageSizeComboBox_SelectionChanged;

                mainPanel.Children.Add(pageSizeLabel);
                mainPanel.Children.Add(_pageSizeComboBox);
            }

            Content = mainPanel;
        }

        private Button CreateNavigationButton(string content, string tooltip, RoutedEventHandler clickHandler)
        {
            var button = new Button
            {
                Content = content,
                Width = 35,
                Height = 30,
                Margin = new Thickness(2, 0, 2, 0),
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand,
                ToolTip = tooltip,
                FontSize = 12
            };

            button.Click += clickHandler;
            return button;
        }

        private Button CreatePageNumberButton(int pageNumber, bool isCurrentPage = false)
        {
            var button = new Button
            {
                Content = pageNumber.ToString(),
                Width = 30,
                Height = 30,
                Margin = new Thickness(1, 0, 1, 0),
                BorderThickness = new Thickness(1),
                Cursor = Cursors.Hand,
                FontSize = 12,
                Tag = pageNumber
            };

            if (isCurrentPage)
            {
                button.Background = new SolidColorBrush(Color.FromRgb(0, 123, 255));
                button.Foreground = Brushes.White;
                button.BorderBrush = new SolidColorBrush(Color.FromRgb(0, 123, 255));
                button.IsEnabled = false;
            }
            else
            {
                button.Background = Brushes.White;
                button.Foreground = new SolidColorBrush(Color.FromRgb(0, 123, 255));
                button.BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230));
            }

            button.Click += PageNumberButton_Click;
            return button;
        }

        #endregion

        #region Event Handlers

        private void FirstPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage > 1)
            {
                CurrentPage = 1;
                OnPageChanged();
            }
        }

        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                OnPageChanged();
            }
        }

        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                OnPageChanged();
            }
        }

        private void LastPage_Click(object sender, RoutedEventArgs e)
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage = TotalPages;
                OnPageChanged();
            }
        }

        private void PageNumberButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pageNumber)
            {
                CurrentPage = pageNumber;
                OnPageChanged();
            }
        }

        private void CurrentPageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (int.TryParse(_currentPageTextBox.Text, out int page))
                {
                    if (page >= 1 && page <= TotalPages)
                    {
                        CurrentPage = page;
                        OnPageChanged();
                    }
                    else
                    {
                        _currentPageTextBox.Text = CurrentPage.ToString();
                    }
                }
                else
                {
                    _currentPageTextBox.Text = CurrentPage.ToString();
                }
            }
        }

        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_pageSizeComboBox.SelectedItem != null)
            {
                var newPageSize = (int)_pageSizeComboBox.SelectedItem;
                if (newPageSize != PageSize)
                {
                    PageSize = newPageSize;
                    OnPageSizeChanged();
                }
            }
        }

        #endregion

        #region Property Changed Handlers

        private static void OnCurrentPageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateControls();
            }
        }

        private static void OnTotalPagesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateControls();
            }
        }

        private static void OnPageSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateControls();
            }
        }

        private static void OnTotalRecordsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PaginationControl control)
            {
                control.UpdateRecordsInfo();
            }
        }

        #endregion

        #region Update Methods

        private void UpdateControls()
        {
            UpdateNavigationButtons();
            UpdatePageNumbers();
            UpdateCurrentPageTextBox();
            UpdateRecordsInfo();
        }

        private void UpdateNavigationButtons()
        {
            if (!ShowNavigationButtons) return;

            _firstPageButton.IsEnabled = CurrentPage > 1;
            _previousPageButton.IsEnabled = CurrentPage > 1;
            _nextPageButton.IsEnabled = CurrentPage < TotalPages;
            _lastPageButton.IsEnabled = CurrentPage < TotalPages;
        }

        private void UpdatePageNumbers()
        {
            if (!ShowPageNumbers || _pageNumbersPanel == null) return;

            _pageNumbersPanel.Children.Clear();

            if (TotalPages <= MaxVisiblePageNumbers)
            {
                // عرض جميع أرقام الصفحات
                for (int i = 1; i <= TotalPages; i++)
                {
                    var button = CreatePageNumberButton(i, i == CurrentPage);
                    _pageNumbersPanel.Children.Add(button);
                }
            }
            else
            {
                // عرض أرقام الصفحات مع النقاط
                var startPage = Math.Max(1, CurrentPage - MaxVisiblePageNumbers / 2);
                var endPage = Math.Min(TotalPages, startPage + MaxVisiblePageNumbers - 1);

                if (endPage - startPage < MaxVisiblePageNumbers - 1)
                {
                    startPage = Math.Max(1, endPage - MaxVisiblePageNumbers + 1);
                }

                // الصفحة الأولى
                if (startPage > 1)
                {
                    _pageNumbersPanel.Children.Add(CreatePageNumberButton(1));
                    if (startPage > 2)
                    {
                        var ellipsis = new TextBlock
                        {
                            Text = "...",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(5, 0, 5, 0)
                        };
                        _pageNumbersPanel.Children.Add(ellipsis);
                    }
                }

                // الصفحات المرئية
                for (int i = startPage; i <= endPage; i++)
                {
                    var button = CreatePageNumberButton(i, i == CurrentPage);
                    _pageNumbersPanel.Children.Add(button);
                }

                // الصفحة الأخيرة
                if (endPage < TotalPages)
                {
                    if (endPage < TotalPages - 1)
                    {
                        var ellipsis = new TextBlock
                        {
                            Text = "...",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(5, 0, 5, 0)
                        };
                        _pageNumbersPanel.Children.Add(ellipsis);
                    }
                    _pageNumbersPanel.Children.Add(CreatePageNumberButton(TotalPages));
                }
            }
        }

        private void UpdateCurrentPageTextBox()
        {
            if (_currentPageTextBox != null)
            {
                _currentPageTextBox.Text = CurrentPage.ToString();
            }

            if (_totalPagesTextBlock != null)
            {
                _totalPagesTextBlock.Text = TotalPages.ToString();
            }
        }

        private void UpdateRecordsInfo()
        {
            if (!ShowRecordsInfo || _recordsInfoTextBlock == null) return;

            var startRecord = ((CurrentPage - 1) * PageSize) + 1;
            var endRecord = Math.Min(CurrentPage * PageSize, TotalRecords);

            if (TotalRecords == 0)
            {
                _recordsInfoTextBlock.Text = "لا توجد سجلات";
            }
            else
            {
                _recordsInfoTextBlock.Text = $"عرض {startRecord}-{endRecord} من {TotalRecords} سجل";
            }
        }

        #endregion

        #region Event Raising

        private void OnPageChanged()
        {
            PageChanged?.Invoke(this, new PageChangedEventArgs(CurrentPage));
        }

        private void OnPageSizeChanged()
        {
            PageSizeChanged?.Invoke(this, new PageSizeChangedEventArgs(PageSize));
        }

        #endregion
    }

    /// <summary>
    /// معاملات حدث تغيير الصفحة
    /// </summary>
    public class PageChangedEventArgs : EventArgs
    {
        public int NewPage { get; }

        public PageChangedEventArgs(int newPage)
        {
            NewPage = newPage;
        }
    }

    /// <summary>
    /// معاملات حدث تغيير حجم الصفحة
    /// </summary>
    public class PageSizeChangedEventArgs : EventArgs
    {
        public int NewPageSize { get; }

        public PageSizeChangedEventArgs(int newPageSize)
        {
            NewPageSize = newPageSize;
        }
    }
}
