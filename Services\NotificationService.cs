using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Services
{
    public class NotificationService
    {
        private readonly MedicalDevicesContext _context;

        public NotificationService(MedicalDevicesContext context)
        {
            _context = context;
        }

        // إضافة إشعار جديد
        public async Task<bool> AddNotificationAsync(string title, string message, string type = "Info", 
            string category = "", int? relatedItemId = null, string relatedItemType = "", string priority = "Medium")
        {
            try
            {
                var notification = new Notification
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Category = category,
                    RelatedItemId = relatedItemId,
                    RelatedItemType = relatedItemType,
                    Priority = priority,
                    IsRead = false,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // الحصول على جميع الإشعارات النشطة
        public async Task<List<Notification>> GetActiveNotificationsAsync()
        {
            return await _context.Notifications
                .Where(n => n.IsActive)
                .OrderByDescending(n => n.CreatedDate)
                .ToListAsync();
        }

        // الحصول على الإشعارات غير المقروءة
        public async Task<List<Notification>> GetUnreadNotificationsAsync()
        {
            return await _context.Notifications
                .Where(n => n.IsActive && !n.IsRead)
                .OrderByDescending(n => n.CreatedDate)
                .ToListAsync();
        }

        // عدد الإشعارات غير المقروءة
        public async Task<int> GetUnreadCountAsync()
        {
            return await _context.Notifications
                .CountAsync(n => n.IsActive && !n.IsRead);
        }

        // تحديد إشعار كمقروء
        public async Task<bool> MarkAsReadAsync(int notificationId)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(notificationId);
                if (notification != null)
                {
                    notification.IsRead = true;
                    notification.ReadDate = DateTime.Now;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        // تحديد جميع الإشعارات كمقروءة
        public async Task<bool> MarkAllAsReadAsync()
        {
            try
            {
                var unreadNotifications = await _context.Notifications
                    .Where(n => n.IsActive && !n.IsRead)
                    .ToListAsync();

                foreach (var notification in unreadNotifications)
                {
                    notification.IsRead = true;
                    notification.ReadDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // حذف إشعار
        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(notificationId);
                if (notification != null)
                {
                    notification.IsActive = false;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        // فحص المخزون المنخفض وإنشاء تنبيهات
        public async Task CheckLowStockAsync()
        {
            try
            {
                var lowStockItems = await _context.InventoryItems
                    .Join(_context.InventorySettings,
                          item => item.Id,
                          settings => settings.InventoryItemId,
                          (item, settings) => new { Item = item, Settings = settings })
                    .Where(x => x.Settings.EnableLowStockAlert && 
                               x.Item.CurrentStock <= x.Settings.MinimumStock)
                    .ToListAsync();

                foreach (var lowStock in lowStockItems)
                {
                    // تحقق من عدم وجود تنبيه مماثل في آخر 24 ساعة
                    var existingAlert = await _context.Notifications
                        .Where(n => n.Category == "LowStock" && 
                                   n.RelatedItemId == lowStock.Item.Id &&
                                   n.CreatedDate > DateTime.Now.AddHours(-24))
                        .FirstOrDefaultAsync();

                    if (existingAlert == null)
                    {
                        await AddNotificationAsync(
                            "تنبيه: مخزون منخفض",
                            $"المنتج '{lowStock.Item.Name}' وصل إلى الحد الأدنى. المخزون الحالي: {lowStock.Item.CurrentStock}، الحد الأدنى: {lowStock.Settings.MinimumStock}",
                            "Warning",
                            "LowStock",
                            lowStock.Item.Id,
                            "InventoryItem",
                            "High"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                await AddNotificationAsync(
                    "خطأ في فحص المخزون",
                    $"حدث خطأ أثناء فحص المخزون المنخفض: {ex.Message}",
                    "Error",
                    "System",
                    null,
                    "",
                    "High"
                );
            }
        }

        // فحص انتهاء الصلاحية وإنشاء تنبيهات
        public async Task CheckExpiryDatesAsync()
        {
            try
            {
                var expiringItems = await _context.InventoryItems
                    .Join(_context.InventorySettings,
                          item => item.Id,
                          settings => settings.InventoryItemId,
                          (item, settings) => new { Item = item, Settings = settings })
                    .Where(x => x.Settings.EnableExpiryAlert && 
                               x.Item.ExpiryDate.HasValue &&
                               x.Item.ExpiryDate.Value <= DateTime.Now.AddDays(x.Settings.ExpiryAlertDays))
                    .ToListAsync();

                foreach (var expiring in expiringItems)
                {
                    // تحقق من عدم وجود تنبيه مماثل في آخر 24 ساعة
                    var existingAlert = await _context.Notifications
                        .Where(n => n.Category == "Expiry" && 
                                   n.RelatedItemId == expiring.Item.Id &&
                                   n.CreatedDate > DateTime.Now.AddHours(-24))
                        .FirstOrDefaultAsync();

                    if (existingAlert == null)
                    {
                        var daysUntilExpiry = (expiring.Item.ExpiryDate.Value - DateTime.Now).Days;
                        var priority = daysUntilExpiry <= 7 ? "Critical" : daysUntilExpiry <= 30 ? "High" : "Medium";

                        await AddNotificationAsync(
                            "تنبيه: انتهاء صلاحية قريب",
                            $"المنتج '{expiring.Item.Name}' سينتهي صلاحيته خلال {daysUntilExpiry} يوم. تاريخ الانتهاء: {expiring.Item.ExpiryDate.Value:dd/MM/yyyy}",
                            "Warning",
                            "Expiry",
                            expiring.Item.Id,
                            "InventoryItem",
                            priority
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                await AddNotificationAsync(
                    "خطأ في فحص انتهاء الصلاحية",
                    $"حدث خطأ أثناء فحص انتهاء الصلاحية: {ex.Message}",
                    "Error",
                    "System",
                    null,
                    "",
                    "High"
                );
            }
        }

        // فحص الصيانة المستحقة
        public async Task CheckMaintenanceDueAsync()
        {
            try
            {
                var devicesDueMaintenance = await _context.MedicalDevices
                    .Where(d => d.Status == "يحتاج صيانة" || 
                               (d.WarrantyEndDate <= DateTime.Now.AddDays(30) && d.WarrantyEndDate > DateTime.Now))
                    .ToListAsync();

                foreach (var device in devicesDueMaintenance)
                {
                    // تحقق من عدم وجود تنبيه مماثل في آخر 7 أيام
                    var existingAlert = await _context.Notifications
                        .Where(n => n.Category == "Maintenance" && 
                                   n.RelatedItemId == device.Id &&
                                   n.CreatedDate > DateTime.Now.AddDays(-7))
                        .FirstOrDefaultAsync();

                    if (existingAlert == null)
                    {
                        var message = device.Status == "يحتاج صيانة" 
                            ? $"الجهاز '{device.Name}' يحتاج إلى صيانة"
                            : $"الجهاز '{device.Name}' ستنتهي ضمانته قريباً في {device.WarrantyEndDate:dd/MM/yyyy}";

                        await AddNotificationAsync(
                            "تنبيه: صيانة مستحقة",
                            message,
                            "Warning",
                            "Maintenance",
                            device.Id,
                            "MedicalDevice",
                            "High"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                await AddNotificationAsync(
                    "خطأ في فحص الصيانة",
                    $"حدث خطأ أثناء فحص الصيانة المستحقة: {ex.Message}",
                    "Error",
                    "System",
                    null,
                    "",
                    "High"
                );
            }
        }
    }
}
