# 🧪 دليل اختبار إدارة الأرقام التسلسلية الشامل

## 📋 **خطة الاختبار الشاملة**

### **المرحلة 1: اختبار البيانات التجريبية الموجودة**

#### **1.1 فحص الجدول الرئيسي:**
- ✅ **افتح التطبيق**
- ✅ **اذهب إلى "الأجهزة الطبية"**
- ✅ **تحقق من وجود الأجهزة التالية:**
  - جهاز الأشعة السينية المحمول (PH001)
  - جهاز الموجات فوق الصوتية (GE002)
  - جهاز تخطيط القلب (SCH003)
- ✅ **تحقق من ظهور الأرقام التسلسلية في عمود "الرقم التسلسلي"**

#### **1.2 اختبار عرض تفاصيل الأجهزة الموجودة:**

**🔍 اختبار جهاز الأشعة السينية:**
1. **انقر مرتين على "جهاز الأشعة السينية المحمول"**
2. **اذهب إلى تبويب "🔢 الأرقام التسلسلية"**
3. **يجب أن تظهر الأرقام التالية:**
   - PH001-MAIN (وحدة التحكم الرئيسية)
   - PH001-TUBE (أنبوب الأشعة السينية)
   - PH001-PANEL (لوحة التحكم)
4. **تحقق من العداد: "إجمالي المكونات: 3 | النشطة: 3"**

**🔍 اختبار جهاز الموجات فوق الصوتية:**
1. **انقر مرتين على "جهاز الموجات فوق الصوتية"**
2. **اذهب إلى تبويب "🔢 الأرقام التسلسلية"**
3. **يجب أن تظهر الأرقام التالية:**
   - GE002-PROBE1 (مسبار البطن)
   - GE002-PROBE2 (مسبار القلب)
   - GE002-SCREEN (شاشة العرض)
4. **تحقق من العداد: "إجمالي المكونات: 3 | النشطة: 3"**

**🔍 اختبار جهاز تخطيط القلب:**
1. **انقر مرتين على "جهاز تخطيط القلب"**
2. **اذهب إلى تبويب "🔢 الأرقام التسلسلية"**
3. **يجب أن تظهر الأرقام التالية:**
   - SCH003-ECG (وحدة تخطيط القلب)
   - SCH003-LEADS (أقطاب القلب)
4. **تحقق من العداد: "إجمالي المكونات: 2 | النشطة: 2"**

### **المرحلة 2: اختبار إضافة جهاز جديد مع أرقام تسلسلية**

#### **2.1 إضافة جهاز جديد:**
1. **اضغط "➕ إضافة جهاز جديد"**
2. **املأ المعلومات التالية:**
   - **الاسم:** جهاز اختبار الأرقام التسلسلية
   - **الماركة:** TestBrand
   - **الموديل:** TEST-2024
   - **الرقم التسلسلي:** MAIN-TEST-001
   - **الفئة:** أجهزة اختبار
   - **الوصف:** جهاز لاختبار إدارة الأرقام التسلسلية
   - **سعر الشراء:** 50000
   - **سعر البيع:** 60000
   - **المورد:** شركة الاختبار
   - **الموقع:** مختبر الاختبار
   - **الحالة:** متاح

#### **2.2 إضافة أرقام تسلسلية متعددة:**
**أضف الأرقام التالية واحداً تلو الآخر:**

1. **الرقم الأول:**
   - الرقم التسلسلي: TEST-CPU-001
   - اسم المكون: وحدة المعالجة المركزية

2. **الرقم الثاني:**
   - الرقم التسلسلي: TEST-DISPLAY-001
   - اسم المكون: شاشة العرض الرئيسية

3. **الرقم الثالث:**
   - الرقم التسلسلي: TEST-SENSOR-001
   - اسم المكون: مستشعر القياس

4. **الرقم الرابع:**
   - الرقم التسلسلي: TEST-POWER-001
   - اسم المكون: وحدة الطاقة

5. **الرقم الخامس:**
   - الرقم التسلسلي: TEST-CABLE-001
   - اسم المكون: كابل الاتصال

#### **2.3 حفظ الجهاز:**
1. **اضغط "حفظ"**
2. **يجب أن تظهر رسالة "تم إضافة الجهاز بنجاح!"**
3. **تحقق من ظهور الجهاز في الجدول الرئيسي**

### **المرحلة 3: التحقق من حفظ الأرقام التسلسلية**

#### **3.1 فحص الجدول الرئيسي:**
1. **تحقق من ظهور الجهاز الجديد في الجدول**
2. **تحقق من ظهور "MAIN-TEST-001" في عمود الرقم التسلسلي**
3. **تأكد من عدم ظهور الأرقام التسلسلية الفرعية كأجهزة منفصلة**

#### **3.2 فحص تفاصيل الجهاز:**
1. **انقر مرتين على الجهاز الجديد**
2. **اذهب إلى تبويب "🔢 الأرقام التسلسلية"**
3. **يجب أن تظهر جميع الأرقام الخمسة:**
   - TEST-CPU-001 (وحدة المعالجة المركزية)
   - TEST-DISPLAY-001 (شاشة العرض الرئيسية)
   - TEST-SENSOR-001 (مستشعر القياس)
   - TEST-POWER-001 (وحدة الطاقة)
   - TEST-CABLE-001 (كابل الاتصال)
4. **تحقق من العداد: "إجمالي المكونات: 5 | النشطة: 5"**

### **المرحلة 4: اختبار تحديث الأرقام التسلسلية**

#### **4.1 تعديل الجهاز:**
1. **اضغط على زر "✏️ تعديل" للجهاز الجديد**
2. **أضف رقمين تسلسليين إضافيين:**
   - TEST-BACKUP-001 (وحدة النسخ الاحتياطي)
   - TEST-NETWORK-001 (وحدة الشبكة)
3. **احذف أحد الأرقام الموجودة (TEST-CABLE-001)**
4. **اضغط "حفظ"**

#### **4.2 التحقق من التحديث:**
1. **افتح تفاصيل الجهاز مرة أخرى**
2. **تحقق من وجود الأرقام الجديدة**
3. **تحقق من عدم وجود الرقم المحذوف**
4. **تحقق من العداد الجديد: "إجمالي المكونات: 6 | النشطة: 6"**

### **المرحلة 5: اختبار زر التحديث**

#### **5.1 اختبار زر "🔄 تحديث القائمة":**
1. **في نافذة تفاصيل الجهاز**
2. **اضغط على زر "🔄 تحديث القائمة"**
3. **يجب أن تظهر رسالة "تم تحديث قائمة الأرقام التسلسلية بنجاح"**
4. **تحقق من أن البيانات لا تزال صحيحة**

### **المرحلة 6: اختبار الحالات الاستثنائية**

#### **6.1 اختبار الأرقام المكررة:**
1. **حاول إضافة رقم تسلسلي موجود بالفعل**
2. **يجب أن تظهر رسالة "هذا الرقم التسلسلي موجود بالفعل!"**

#### **6.2 اختبار الحقول الفارغة:**
1. **حاول إضافة رقم تسلسلي فارغ**
2. **يجب أن تظهر رسالة تحذيرية**

### **المرحلة 7: اختبار الأداء**

#### **7.1 اختبار مع عدد كبير من الأرقام:**
1. **أضف جهازاً جديداً**
2. **أضف 20 رقم تسلسلي مختلف**
3. **تحقق من سرعة التحميل والعرض**
4. **تحقق من دقة العداد**

## ✅ **معايير النجاح:**

### **يعتبر الاختبار ناجحاً إذا:**
1. ✅ **جميع البيانات التجريبية تظهر بشكل صحيح**
2. ✅ **الأرقام التسلسلية تُحفظ مع الأجهزة الجديدة**
3. ✅ **الأرقام التسلسلية تظهر في تفاصيل الجهاز فقط**
4. ✅ **لا تظهر الأرقام التسلسلية كأجهزة منفصلة في الجدول الرئيسي**
5. ✅ **يمكن تحديث الأرقام التسلسلية بنجاح**
6. ✅ **زر التحديث يعمل بشكل صحيح**
7. ✅ **معالجة الأخطاء تعمل بشكل صحيح**
8. ✅ **العدادات تظهر الأرقام الصحيحة**
9. ✅ **لا توجد أخطاء أو تحذيرات في وحدة التحكم**
10. ✅ **الأداء مقبول حتى مع عدد كبير من الأرقام**

## 🚨 **في حالة فشل أي اختبار:**
1. **سجل تفاصيل الخطأ بدقة**
2. **التقط لقطة شاشة إن أمكن**
3. **تحقق من وحدة التحكم (Debug Console) للحصول على معلومات إضافية**
4. **أبلغ عن المشكلة مع جميع التفاصيل**

---

## 🎯 **ابدأ الاختبار الآن!**

**اتبع الخطوات أعلاه بالترتيب وأبلغني بالنتائج!** 🚀
