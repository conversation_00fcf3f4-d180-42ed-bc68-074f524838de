🔧 المعلومات التقنية - نظام إدارة الأجهزة الطبية
================================================

📊 تفاصيل التقنية:
- Framework: .NET 6.0
- UI Technology: WPF (Windows Presentation Foundation)
- Database: SQLite with Entity Framework Core
- Architecture: Self-Contained Single File
- Target Platform: Windows x64

🗄️ قاعدة البيانات:
- اسم الملف: MedicalDevicesIntegrated.db
- النوع: SQLite Database
- الحجم التقريبي: متغير حسب البيانات
- التشفير: غير مشفرة (للأداء الأمثل)

📋 الجداول الرئيسية:
- MedicalDevices (الأجهزة الطبية)
- DeviceSerialNumbers (الأرقام التسلسلية)
- Customers (العملاء)
- Sales (المبيعات)
- InventoryItems (المخزون)
- MaintenanceRecords (سجلات الصيانة)
- Suppliers (الموردين)
- Shipments (الشحنات)

🔧 الجداول المتقدمة:
- SmartAlerts (التنبيهات الذكية)
- Notifications (الإشعارات)
- BackupRecords (سجلات النسخ الاحتياطي)
- ExportRecords (سجلات التصدير)
- SearchIndexes (فهارس البحث)

⚙️ إعدادات الأداء:
- Connection Pooling: مفعل
- Auto Save: مفعل
- Detailed Errors: مفعل للتشخيص
- Sensitive Data Logging: مفعل للتطوير

🛡️ الأمان والاستقرار:
- Context Management: محسن لمنع تسريب الذاكرة
- Auto Dispose: آمن ومحمي
- Error Handling: شامل مع تسجيل مفصل
- Data Validation: على مستوى قاعدة البيانات

📁 هيكل الملفات:
MedicalDevicesManager.exe     - الملف التنفيذي الرئيسي
MedicalDevicesIntegrated.db   - قاعدة البيانات
README_التشغيل.txt           - دليل المستخدم
TECHNICAL_INFO.txt           - هذا الملف

🔄 آلية الحفظ:
1. حفظ تلقائي عند إضافة/تعديل البيانات
2. حفظ عند إغلاق النوافذ
3. حفظ نهائي عند إغلاق التطبيق
4. حماية من فقدان البيانات

💾 النسخ الاحتياطي:
- يمكن إنشاء نسخ احتياطية من داخل البرنامج
- النسخ الاحتياطية تشمل جميع البيانات
- يمكن استعادة النسخ الاحتياطية بسهولة

🚀 الأداء:
- بدء تشغيل سريع
- استجابة فورية للواجهة
- استهلاك ذاكرة محسن
- عمليات قاعدة بيانات محسنة

📈 الإحصائيات:
- عدد الجداول: 20+ جدول
- عدد النوافذ: 30+ نافذة
- عدد الوظائف: 100+ وظيفة
- اللغة: العربية بالكامل

تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والاستقرار الأمثل.
