using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using MedicalDevicesManager.Services;

namespace MedicalDevicesManager.Windows
{
    public partial class ExportWindow : Window
    {
        private ExportService _exportService;
        private List<ExportRecord> _allExports;
        
        public ExportWindow()
        {
            InitializeComponent();
            _exportService = new ExportService(App.DatabaseContext);

            // تعيين التواريخ الافتراضية
            FromDatePicker.SelectedDate = DateTime.Now.AddMonths(-1);
            ToDatePicker.SelectedDate = DateTime.Now;

            // تعيين اسم افتراضي للملف
            FileNameTextBox.Text = $"Export_{DateTime.Now:yyyyMMdd_HHmmss}";

            LoadExportsAsync();
        }
        
        private async void LoadExportsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري التحميل...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                _allExports = await _exportService.GetAllExportsAsync();
                ExportsDataGrid.ItemsSource = _allExports;
                
                // تحديث الإحصائيات
                UpdateStatistics();
                
                StatusTextBlock.Text = "جاهز";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في التحميل";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في تحميل سجلات التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateStatistics()
        {
            if (_allExports != null)
            {
                TotalExportsTextBlock.Text = $"إجمالي التصديرات: {_allExports.Count}";
                
                var successfulExports = _allExports.Where(e => e.Status == "Success").ToList();
                SuccessfulExportsTextBlock.Text = $"التصديرات الناجحة: {successfulExports.Count}";
                
                var totalSize = successfulExports.Sum(e => e.FileSize);
                TotalSizeTextBlock.Text = $"الحجم الإجمالي: {FormatFileSize(totalSize)}";
            }
        }
        
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        
        private async void ExportDevicesBtn_Click(object sender, RoutedEventArgs e)
        {
            await ExportWithProgress("الأجهزة الطبية", async () =>
            {
                var fileName = $"MedicalDevices_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return await _exportService.ExportDevicesToExcelAsync(fileName, FromDatePicker.SelectedDate, ToDatePicker.SelectedDate);
            });
        }
        
        private async void ExportInventoryBtn_Click(object sender, RoutedEventArgs e)
        {
            await ExportWithProgress("المخزون", async () =>
            {
                var fileName = $"Inventory_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return await _exportService.ExportInventoryToExcelAsync(fileName);
            });
        }
        
        private async void ExportSalesBtn_Click(object sender, RoutedEventArgs e)
        {
            await ExportWithProgress("المبيعات", async () =>
            {
                // سيتم تنفيذ هذه الوظيفة لاحقاً
                await Task.Delay(100); // إضافة await للتخلص من التحذير
                MessageBox.Show("تصدير المبيعات سيتم إضافته قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                return false;
            });
        }
        
        private async void CustomExportBtn_Click(object sender, RoutedEventArgs e)
        {
            if (DataTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع البيانات", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(FileNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الملف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                FileNameTextBox.Focus();
                return;
            }
            
            var dataType = (DataTypeComboBox.SelectedItem as ComboBoxItem)?.Tag.ToString();
            var exportType = (ExportTypeComboBox.SelectedItem as ComboBoxItem)?.Tag.ToString();
            var fileName = FileNameTextBox.Text.Trim();
            
            // إضافة الامتداد المناسب
            if (exportType == "Excel" && !fileName.EndsWith(".xlsx"))
            {
                fileName += ".xlsx";
            }
            else if (exportType == "PDF" && !fileName.EndsWith(".pdf"))
            {
                fileName += ".pdf";
            }
            
            await ExportWithProgress($"{(DataTypeComboBox.SelectedItem as ComboBoxItem)?.Content}", async () =>
            {
                switch (dataType)
                {
                    case "MedicalDevices":
                        return await _exportService.ExportDevicesToExcelAsync(fileName, FromDatePicker.SelectedDate, ToDatePicker.SelectedDate);
                    
                    case "Inventory":
                        return await _exportService.ExportInventoryToExcelAsync(fileName);
                    
                    default:
                        MessageBox.Show($"تصدير {dataType} غير مدعوم حالياً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                        return false;
                }
            });
        }
        
        private async System.Threading.Tasks.Task ExportWithProgress(string dataTypeName, Func<System.Threading.Tasks.Task<bool>> exportAction)
        {
            try
            {
                // إظهار شريط التقدم
                ExportProgressBar.Visibility = Visibility.Visible;
                StatusTextBlock.Text = $"جاري تصدير {dataTypeName}...";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
                
                // تعطيل الأزرار
                ExportDevicesBtn.IsEnabled = false;
                ExportInventoryBtn.IsEnabled = false;
                ExportSalesBtn.IsEnabled = false;
                CustomExportBtn.IsEnabled = false;
                
                var success = await exportAction();
                
                if (success)
                {
                    StatusTextBlock.Text = $"تم تصدير {dataTypeName} بنجاح";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                    MessageBox.Show($"تم تصدير {dataTypeName} بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = $"فشل في تصدير {dataTypeName}";
                    StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                }
                
                LoadExportsAsync();
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"خطأ في تصدير {dataTypeName}";
                StatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في تصدير {dataTypeName}: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إخفاء شريط التقدم وإعادة تفعيل الأزرار
                ExportProgressBar.Visibility = Visibility.Collapsed;
                ExportDevicesBtn.IsEnabled = true;
                ExportInventoryBtn.IsEnabled = true;
                ExportSalesBtn.IsEnabled = true;
                CustomExportBtn.IsEnabled = true;
            }
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadExportsAsync();
        }
        
        private void OpenFileBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var export = button?.DataContext as ExportRecord;
            
            if (export != null && File.Exists(export.FilePath))
            {
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = export.FilePath,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private void OpenFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var export = button?.DataContext as ExportRecord;
            
            if (export != null && File.Exists(export.FilePath))
            {
                try
                {
                    var folderPath = Path.GetDirectoryName(export.FilePath);
                    Process.Start("explorer.exe", folderPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح المجلد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async void DeleteExportBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var export = button?.DataContext as ExportRecord;
            
            if (export != null)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف ملف التصدير '{export.ExportName}'؟\n\nلا يمكن التراجع عن هذا الإجراء!",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _exportService.DeleteExportAsync(export.Id);
                        
                        if (success)
                        {
                            MessageBox.Show("تم حذف ملف التصدير بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadExportsAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف ملف التصدير!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف ملف التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private void ExportsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا عند تحديد ملف تصدير
        }
    }
}
