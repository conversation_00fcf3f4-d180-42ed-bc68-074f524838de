<Window x:Class="MedicalDevicesManager.Windows.BackupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="النسخ الاحتياطي" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="💾 النسخ الاحتياطي" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="CreateBackupBtn" Content="💾 إنشاء نسخة احتياطية" Width="180" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="CreateBackupBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="AutoBackupBtn" Content="⚡ نسخة تلقائية" Width="130" Height="40" 
                    Background="#17A2B8" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AutoBackupBtn_Click"/>
            
            <Button Grid.Column="2" x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="40" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="RefreshBtn_Click"/>
            
            <Button Grid.Column="3" x:Name="CleanupBtn" Content="🧹 تنظيف القديم" Width="130" Height="40" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="CleanupBtn_Click"/>
            
            <TextBlock Grid.Column="5" x:Name="StatusTextBlock" Text="جاهز" VerticalAlignment="Center" 
                       FontWeight="Bold" Foreground="#28A745"/>
        </Grid>
        
        <!-- جدول النسخ الاحتياطية -->
        <DataGrid Grid.Row="2" x:Name="BackupsDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14"
                  SelectionChanged="BackupsDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="الحالة" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Status}" HorizontalAlignment="Center" FontWeight="Bold">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status}" Value="Success">
                                                <Setter Property="Foreground" Value="#28A745"/>
                                                <Setter Property="Text" Value="✅ نجح"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                <Setter Property="Foreground" Value="#DC3545"/>
                                                <Setter Property="Text" Value="❌ فشل"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="InProgress">
                                                <Setter Property="Foreground" Value="#FFC107"/>
                                                <Setter Property="Text" Value="⏳ جاري"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTextColumn Header="اسم النسخة" Binding="{Binding BackupName}" Width="200"/>
                <DataGridTextColumn Header="النوع" Binding="{Binding BackupType}" Width="100"/>
                <DataGridTextColumn Header="الحجم" Binding="{Binding FileSize}" Width="100"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" Width="130"/>
                <DataGridTextColumn Header="تاريخ الاكتمال" Binding="{Binding CompletedDate, StringFormat=dd/MM/yyyy HH:mm}" Width="130"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="🔄" Width="30" Height="25" 
                                        Background="#28A745" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="استعادة"
                                        Click="RestoreBtn_Click"/>
                                <Button Content="✅" Width="30" Height="25" 
                                        Background="#17A2B8" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="فحص السلامة"
                                        Click="VerifyBtn_Click"/>
                                <Button Content="📁" Width="30" Height="25" 
                                        Background="#6C757D" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="فتح المجلد"
                                        Click="OpenFolderBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteBackupBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج إنشاء نسخة احتياطية -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            
            <StackPanel>
                <TextBlock Text="📝 إنشاء نسخة احتياطية جديدة" FontWeight="Bold" Margin="0,0,0,15" Foreground="#495057"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="اسم النسخة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" x:Name="BackupNameTextBox" Height="30" Margin="0,0,20,0"
                             Text="Backup_Manual"/>
                    
                    <TextBlock Grid.Column="2" Text="الوصف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="3" x:Name="DescriptionTextBox" Height="30" Margin="0,0,20,0" 
                             Text="نسخة احتياطية يدوية"/>
                    
                    <Button Grid.Column="4" x:Name="CreateManualBackupBtn" Content="💾 إنشاء" Width="100" Height="30" 
                            Background="#28A745" Foreground="White" BorderThickness="0" 
                            FontSize="12" FontWeight="SemiBold" Click="CreateManualBackupBtn_Click"/>
                </Grid>
                
                <!-- شريط التقدم -->
                <ProgressBar x:Name="BackupProgressBar" Height="20" Margin="0,15,0,0" 
                             Visibility="Collapsed" IsIndeterminate="True"/>
                
                <!-- معلومات إضافية -->
                <Grid Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" x:Name="TotalBackupsTextBlock" Text="إجمالي النسخ: 0" 
                               FontWeight="SemiBold" Foreground="#495057"/>
                    <TextBlock Grid.Column="1" x:Name="SuccessfulBackupsTextBlock" Text="النسخ الناجحة: 0" 
                               FontWeight="SemiBold" Foreground="#28A745" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Column="2" x:Name="TotalSizeTextBlock" Text="الحجم الإجمالي: 0 MB" 
                               FontWeight="SemiBold" Foreground="#17A2B8" HorizontalAlignment="Right"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
    

</Window>
