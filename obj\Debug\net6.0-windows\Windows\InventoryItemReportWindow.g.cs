﻿#pragma checksum "..\..\..\..\Windows\InventoryItemReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "311C34B588089C0847BE0B8053FEEFB6C339B214"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// InventoryItemReportWindow
    /// </summary>
    public partial class InventoryItemReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventoryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalItemsText;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalValueText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockText;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintBtn;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelBtn;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/inventoryitemreportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 43 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoryFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 48 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InventoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 5:
            this.TotalItemsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TotalValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LowStockText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PrintBtn = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.PrintBtn.Click += new System.Windows.RoutedEventHandler(this.PrintBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ExportExcelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.ExportExcelBtn.Click += new System.Windows.RoutedEventHandler(this.ExportExcelBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ExportPdfBtn = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\Windows\InventoryItemReportWindow.xaml"
            this.ExportPdfBtn.Click += new System.Windows.RoutedEventHandler(this.ExportPdfBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

