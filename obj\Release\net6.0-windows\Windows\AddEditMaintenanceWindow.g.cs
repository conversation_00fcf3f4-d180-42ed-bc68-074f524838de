﻿#pragma checksum "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ECE7EFCB91F9537B2D13AA14300552EE29605B60"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditMaintenanceWindow
    /// </summary>
    public partial class AddEditMaintenanceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DeviceComboBox;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SerialNumberComboBox;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MaintenanceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker MaintenanceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NextMaintenanceDatePanel;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker NextMaintenanceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TechnicianComboBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageTechniciansBtn;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReportBookletPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseReportBtn;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewReportBtn;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveReportBtn;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdatedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditmaintenancewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DeviceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 25 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.DeviceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DeviceComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SerialNumberComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.MaintenanceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.MaintenanceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 61 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.StatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.NextMaintenanceDatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.NextMaintenanceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.CostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TechnicianComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.ManageTechniciansBtn = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.ManageTechniciansBtn.Click += new System.Windows.RoutedEventHandler(this.ManageTechniciansBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ReportBookletPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.BrowseReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.BrowseReportBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ViewReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.ViewReportBtn.Click += new System.Windows.RoutedEventHandler(this.ViewReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.RemoveReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 143 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.RemoveReportBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.DeviceInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LastUpdatedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

