using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditShipmentWindow : Window
    {
        private Shipment _shipment;
        private bool _isEditMode;
        
        public AddEditShipmentWindow(Shipment shipment = null)
        {
            InitializeComponent();
            _shipment = shipment;
            _isEditMode = shipment != null;
            
            LoadDataAsync();
            LoadRecipientsAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل شحنة";
                LoadShipmentData();
            }
            else
            {
                // تعيين القيم الافتراضية
                ShipmentDatePicker.SelectedDate = DateTime.Now;
                StatusComboBox.SelectedIndex = 0;
                ShippingCompanyComboBox.SelectedIndex = 0;
                GenerateShipmentNumber();
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            }
        }
        
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل العملاء كمستلمين محتملين
                var customers = await App.DatabaseContext.Customers
                    .Where(c => c.Status == "نشط")
                    .ToListAsync();
                RecipientComboBox.ItemsSource = customers;
                
                if (!_isEditMode && customers.Any())
                    RecipientComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void GenerateShipmentNumber()
        {
            var shipmentNumber = $"SHP-{DateTime.Now:yyyy}-{DateTime.Now:MMddHHmmss}";
            ShipmentNumberTextBox.Text = shipmentNumber;
        }
        
        private void LoadShipmentData()
        {
            if (_shipment == null) return;
            
            ShipmentNumberTextBox.Text = _shipment.ShipmentNumber;
            ShipmentDatePicker.SelectedDate = _shipment.ShipmentDate;
            ReceiptDatePicker.SelectedDate = _shipment.ReceiptDate;
            DeliveryAddressTextBox.Text = _shipment.DeliveryAddress;
            TotalValueTextBox.Text = _shipment.TotalValue.ToString();
            ShippingCostTextBox.Text = _shipment.ShippingCost.ToString();
            TrackingNumberTextBox.Text = _shipment.TrackingNumber;

            // تحميل أوراق الشحنة
            if (!string.IsNullOrEmpty(_shipment.ShipmentDocumentsPath))
            {
                ShipmentDocumentsPathTextBox.Text = Path.GetFileName(_shipment.ShipmentDocumentsPath);
                ShipmentDocumentsPathTextBox.Tag = _shipment.ShipmentDocumentsPath;
                ViewShipmentDocumentsBtn.IsEnabled = File.Exists(_shipment.ShipmentDocumentsPath);
                RemoveShipmentDocumentsBtn.IsEnabled = true;
            }
            CreatedDateTextBlock.Text = _shipment.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            LastUpdatedTextBlock.Text = _shipment.LastUpdated.ToString("dd/MM/yyyy HH:mm");
            
            // تعيين المستلم
            RecipientComboBox.Text = _shipment.RecipientName;
            
            // تعيين الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == _shipment.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private void RecipientComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (RecipientComboBox.SelectedItem is Customer customer)
            {
                DeliveryAddressTextBox.Text = $"{customer.Address}, {customer.City} {customer.PostalCode}";
                RecipientInfoTextBlock.Text = $"العميل: {customer.Name} - النوع: {customer.CustomerType} - الهاتف: {customer.Phone}";
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateShipmentData();
                    App.DatabaseContext.Shipments.Update(_shipment);
                }
                else
                {
                    _shipment = new Shipment();
                    UpdateShipmentData();
                    _shipment.CreatedDate = DateTime.Now;
                    App.DatabaseContext.Shipments.Add(_shipment);
                }
                
                _shipment.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث الشحنة بنجاح!" : "تم إضافة الشحنة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateShipmentData()
        {
            _shipment.ShipmentNumber = ShipmentNumberTextBox.Text.Trim();
            _shipment.ShipmentDate = ShipmentDatePicker.SelectedDate ?? DateTime.Now;
            _shipment.ReceiptDate = ReceiptDatePicker.SelectedDate;
            _shipment.RecipientName = RecipientComboBox.Text;
            _shipment.DeliveryAddress = DeliveryAddressTextBox.Text.Trim();
            _shipment.Status = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
            _shipment.TotalValue = decimal.Parse(TotalValueTextBox.Text);
            _shipment.ShippingCost = decimal.Parse(ShippingCostTextBox.Text);
            _shipment.TrackingNumber = TrackingNumberTextBox.Text.Trim();
            _shipment.ShipmentDocumentsPath = ShipmentDocumentsPathTextBox.Text != "لم يتم اختيار ملف" ?
                                           ShipmentDocumentsPathTextBox.Tag?.ToString() ?? "" : "";
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(ShipmentNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الشحنة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (ShipmentDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الشحن", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (RecipientComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المستلم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(DeliveryAddressTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان التسليم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الشحنة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(TotalValueTextBox.Text, out decimal totalValue) || totalValue <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة شحنة صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(ShippingCostTextBox.Text, out decimal shippingCost) || shippingCost < 0)
            {
                MessageBox.Show("يرجى إدخال تكلفة شحن صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async void LoadRecipientsAsync()
        {
            try
            {
                var recipients = await App.DatabaseContext.RecipientNames
                    .Where(r => r.IsActive)
                    .OrderBy(r => r.Name)
                    .ToListAsync();

                RecipientComboBox.ItemsSource = recipients;

                // إضافة مستلمين افتراضيين إذا لم يوجدوا
                if (!recipients.Any())
                {
                    var defaultRecipients = new[]
                    {
                        new RecipientName { Name = "مدير المستشفى", Phone = "", Address = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new RecipientName { Name = "مسؤول المخزون", Phone = "", Address = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new RecipientName { Name = "رئيس القسم", Phone = "", Address = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new RecipientName { Name = "المدير المالي", Phone = "", Address = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new RecipientName { Name = "مسؤول الاستلام", Phone = "", Address = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now }
                    };

                    App.DatabaseContext.RecipientNames.AddRange(defaultRecipients);
                    await App.DatabaseContext.SaveChangesAsync();

                    RecipientComboBox.ItemsSource = defaultRecipients;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أسماء المستلمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageRecipientsBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageRecipientsWindow = new ManageRecipientsWindow();
            if (manageRecipientsWindow.ShowDialog() == true)
            {
                LoadRecipientsAsync();
            }
        }

        private void BrowseShipmentDocumentsBtn_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "اختيار أوراق الشحنة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.docx)|*.docx|ملفات Excel (*.xlsx)|*.xlsx|الصور (*.jpg;*.png)|*.jpg;*.png",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    // إنشاء مجلد الوثائق إذا لم يكن موجوداً
                    string documentsFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents", "Shipments");
                    Directory.CreateDirectory(documentsFolder);

                    // إنشاء اسم ملف فريد
                    string fileName = $"Shipment_{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(openFileDialog.FileName)}";
                    string destinationPath = Path.Combine(documentsFolder, fileName);

                    // نسخ الملف
                    File.Copy(openFileDialog.FileName, destinationPath, true);

                    // تحديث واجهة المستخدم
                    ShipmentDocumentsPathTextBox.Text = Path.GetFileName(destinationPath);
                    ShipmentDocumentsPathTextBox.Tag = destinationPath;
                    ViewShipmentDocumentsBtn.IsEnabled = true;
                    RemoveShipmentDocumentsBtn.IsEnabled = true;

                    MessageBox.Show("تم رفع أوراق الشحنة بنجاح!", "نجح الرفع", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في رفع الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ViewShipmentDocumentsBtn_Click(object sender, RoutedEventArgs e)
        {
            string filePath = ShipmentDocumentsPathTextBox.Tag?.ToString();
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RemoveShipmentDocumentsBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إزالة أوراق الشحنة؟", "تأكيد الإزالة",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ShipmentDocumentsPathTextBox.Text = "لم يتم اختيار ملف";
                ShipmentDocumentsPathTextBox.Tag = null;
                ViewShipmentDocumentsBtn.IsEnabled = false;
                RemoveShipmentDocumentsBtn.IsEnabled = false;
            }
        }
    }
}
