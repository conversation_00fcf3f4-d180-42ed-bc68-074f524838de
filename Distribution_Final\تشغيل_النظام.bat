@echo off
title نظام إدارة الأجهزة الطبية المتكامل
echo.
echo ========================================
echo    نظام إدارة الأجهزة الطبية المتكامل
echo ========================================
echo.
echo جاري تشغيل النظام...
echo.

REM التحقق من وجود الملفات المطلوبة
if not exist "MedicalDevicesManager.exe" (
    echo خطأ: ملف البرنامج غير موجود!
    echo تأكد من وجود MedicalDevicesManager.exe
    pause
    exit /b 1
)

if not exist "MedicalDevicesIntegrated.db" (
    echo تحذير: ملف قاعدة البيانات غير موجود!
    echo سيتم إنشاء قاعدة بيانات جديدة...
    echo.
)

REM تشغيل البرنامج
start "" "MedicalDevicesManager.exe"

echo تم تشغيل النظام بنجاح!
echo يمكنك إغلاق هذه النافذة الآن.
echo.
timeout /t 3 /nobreak >nul
exit
