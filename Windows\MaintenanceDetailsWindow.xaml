<Window x:Class="MedicalDevicesManager.Windows.MaintenanceDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل سجل الصيانة" Height="700" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        FlowDirection="RightToLeft" FontFamily="Segoe UI">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="📋 تفاصيل سجل الصيانة الشاملة" 
                   FontSize="24" FontWeight="Bold" Margin="0,0,0,20"
                   Foreground="#2C3E50" HorizontalAlignment="Center"/>
        
        <!-- المحتوى -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- معلومات الجهاز -->
                <Border Background="#E8F4FD" Padding="15" CornerRadius="8" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🏥 معلومات الجهاز" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" Foreground="#1976D2"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="اسم الجهاز:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="DeviceNameTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="الرقم التسلسلي:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="SerialNumberTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="نوع الصيانة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="MaintenanceTypeTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="الحالة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="StatusTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- معلومات الصيانة -->
                <Border Background="#F0F8E8" Padding="15" CornerRadius="8" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🛠️ معلومات الصيانة" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" Foreground="#388E3C"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="تاريخ الصيانة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="MaintenanceDateTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="الصيانة القادمة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="NextMaintenanceDateTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="اسم الفني:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TechnicianNameTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>

                                <TextBlock Text="الشركة المجهزة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="SupplierCompanyTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="التكلفة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="CostTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="تاريخ الإنشاء:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="آخر تحديث:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="" FontSize="14" Margin="0,0,0,10"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- الوصف والملاحظات -->
                <Border Background="#FFF8E1" Padding="15" CornerRadius="8" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📝 الوصف والملاحظات" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" Foreground="#F57C00"/>
                        
                        <TextBlock Text="وصف الصيانة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBlock x:Name="DescriptionTextBlock" Text="" FontSize="14" Margin="0,0,0,10" TextWrapping="Wrap"/>
                        
                        <TextBlock Text="الملاحظات:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBlock x:Name="NotesTextBlock" Text="" FontSize="14" Margin="0,0,0,10" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>
                
                <!-- الملفات والمرفقات -->
                <Border Background="#F3E5F5" Padding="15" CornerRadius="8" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📎 الملفات والمرفقات" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" Foreground="#7B1FA2"/>
                        
                        <!-- كتيب تقرير الصيانة -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="📋 كتيب تقرير الصيانة:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock Grid.Column="1" x:Name="ReportBookletTextBlock" Text="لا يوجد كتيب مرفق" FontSize="14" VerticalAlignment="Center"/>
                            <Button Grid.Column="2" x:Name="ViewReportBtn" Content="👁️ عرض" Width="80" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Click="ViewReportBtn_Click"
                                    IsEnabled="False"/>
                        </Grid>
                        
                        <!-- الأوراق والمخاطبات -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="📄 الأوراق والمخاطبات:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock Grid.Column="1" x:Name="DocumentsTextBlock" Text="لا توجد أوراق مرفقة" FontSize="14" VerticalAlignment="Center"/>
                            <Button Grid.Column="2" x:Name="ViewDocumentsBtn" Content="👁️ عرض" Width="80" Height="30"
                                    Background="#2196F3" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Click="ViewDocumentsBtn_Click"
                                    IsEnabled="False"/>
                        </Grid>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="EditButton" Content="✏️ تعديل سجل الصيانة" Width="180" Height="40" 
                    Background="#FF9800" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="EditButton_Click"/>
            <Button x:Name="CloseButton" Content="❌ إغلاق" Width="120" Height="40" 
                    Background="#607D8B" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
