<Window x:Class="MedicalDevicesManager.Windows.ManageSerialNumbersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأرقام التسلسلية" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان ومعلومات الجهاز -->
        <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="🔢 إدارة الأرقام التسلسلية" 
                           FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" 
                           Margin="0,0,0,10" Foreground="#1976D2"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="الجهاز:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Grid.Column="1" x:Name="DeviceNameTextBlock" Text="" FontWeight="SemiBold" 
                               Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="الموديل:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Grid.Column="3" x:Name="DeviceModelTextBlock" Text="" FontWeight="SemiBold" 
                               Foreground="#1976D2" VerticalAlignment="Center"/>
                </Grid>
            </StackPanel>
        </Border>
        
        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="AddSerialBtn" Content="➕ إضافة رقم تسلسلي" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AddSerialBtn_Click"/>
            
            <!-- تم نقل وظائف التعديل والحذف إلى AdvancedDataGrid -->
            
            <Button Grid.Column="4" x:Name="CloseBtn" Content="❌ إغلاق" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CloseBtn_Click"/>
        </Grid>
        
        <!-- جدول الأرقام التسلسلية المطور -->
        <ScrollViewer Grid.Row="2" x:Name="SerialNumbersScrollViewer" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <!-- سيتم إنشاء AdvancedDataGrid هنا برمجياً -->
        </ScrollViewer>
        
        <!-- نموذج إضافة/تعديل الرقم التسلسلي -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0" x:Name="EditPanel" Visibility="Collapsed">
            
            <StackPanel>
                <TextBlock x:Name="EditPanelTitle" Text="📝 إضافة رقم تسلسلي جديد" FontWeight="Bold" 
                           Margin="0,0,0,15" Foreground="#495057"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- الصف الأول -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="الرقم التسلسلي:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="1" x:Name="SerialNumberTextBox" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="اسم المكون:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="3" x:Name="ComponentNameTextBox" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="4" Text="نوع المكون:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Column="5" x:Name="ComponentTypeComboBox" Height="30">
                            <ComboBoxItem Content="رئيسي" IsSelected="True"/>
                            <ComboBoxItem Content="قطعة غيار"/>
                            <ComboBoxItem Content="ملحق"/>
                            <ComboBoxItem Content="مستهلك"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>
                    </Grid>
                    
                    <!-- الصف الثاني -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="الموقع:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="1" x:Name="LocationTextBox" Height="30" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Column="2" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Column="3" x:Name="StatusComboBox" Height="30" Margin="0,0,20,0">
                            <ComboBoxItem Content="نشط" IsSelected="True"/>
                            <ComboBoxItem Content="معطل"/>
                            <ComboBoxItem Content="مفقود"/>
                            <ComboBoxItem Content="تحت الصيانة"/>
                        </ComboBox>
                        
                        <TextBlock Grid.Column="4" Text="تاريخ التركيب:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <DatePicker Grid.Column="5" x:Name="InstallationDatePicker" Height="30"/>
                    </Grid>
                    
                    <!-- الصف الثالث -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="الوصف:" VerticalAlignment="Top" Margin="0,5,10,0"/>
                        <TextBox Grid.Column="1" x:Name="DescriptionTextBox" Height="60" Margin="0,0,20,0"
                                 TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        
                        <TextBlock Grid.Column="2" Text="الملاحظات:" VerticalAlignment="Top" Margin="0,5,10,0"/>
                        <TextBox Grid.Column="3" x:Name="NotesTextBox" Height="60" Margin="0,0,20,0"
                                 TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        
                        <StackPanel Grid.Column="4" Orientation="Vertical">
                            <Button x:Name="SaveBtn" Content="💾 حفظ" Width="100" Height="30" 
                                    Background="#28A745" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Margin="0,0,0,5" Click="SaveBtn_Click"/>
                            <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="100" Height="30" 
                                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                                    FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
