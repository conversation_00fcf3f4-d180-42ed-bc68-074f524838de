<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="MedicalDevicesManager.Windows.InventoryItemReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📊 تقرير المخزون" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8F9FA" FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#6F42C1" Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="تقرير المخزون الشامل" FontSize="20" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="10" CornerRadius="8"
                BorderBrush="#E9ECEF" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="الفئة:" FontWeight="SemiBold" 
                          VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="CategoryFilterComboBox" Height="35" 
                         Margin="0,0,20,0" SelectionChanged="CategoryFilter_SelectionChanged"/>

                <TextBlock Grid.Column="2" Text="الحالة:" FontWeight="SemiBold" 
                          VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="3" x:Name="StatusFilterComboBox" Height="35" 
                         Margin="0,0,20,0" SelectionChanged="StatusFilter_SelectionChanged">
                    <ComboBoxItem Content="الكل" IsSelected="True"/>
                    <ComboBoxItem Content="متاح"/>
                    <ComboBoxItem Content="نفد"/>
                    <ComboBoxItem Content="محجوز"/>
                </ComboBox>

                <Button Grid.Column="5" x:Name="RefreshBtn" Content="🔄 تحديث" Height="35" Width="120"
                        Background="#28A745" Foreground="White" BorderThickness="0"
                        FontWeight="SemiBold" Click="RefreshBtn_Click"/>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="2" Background="White" Margin="10" CornerRadius="8"
                BorderBrush="#E9ECEF" BorderThickness="1">
            <DataGrid x:Name="InventoryDataGrid" AutoGenerateColumns="False" 
                     CanUserAddRows="False" CanUserDeleteRows="False"
                     GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                     Background="White" RowBackground="White" AlternatingRowBackground="#F8F9FA"
                     BorderThickness="0" Margin="10">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="200" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الكمية المتاحة" Binding="{Binding AvailableQuantity}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumStock}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat=C}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="القيمة الإجمالية" Binding="{Binding TotalValue, StringFormat=C}" Width="140" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="تاريخ آخر تحديث" Binding="{Binding LastUpdated, StringFormat=dd/MM/yyyy}" Width="140" IsReadOnly="True"/>
                </DataGrid.Columns>

                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#6F42C1"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>
            </DataGrid>
        </Border>

        <!-- Summary and Actions -->
        <Border Grid.Row="3" Background="White" Padding="20" Margin="10,0,10,10" CornerRadius="8"
                BorderBrush="#E9ECEF" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Summary -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="TotalItemsText" Text="إجمالي الأصناف: 0" FontWeight="SemiBold" 
                              Margin="0,0,20,0" VerticalAlignment="Center"/>
                    <TextBlock x:Name="TotalValueText" Text="القيمة الإجمالية: 0.00 ر.س" FontWeight="SemiBold" 
                              Margin="0,0,20,0" VerticalAlignment="Center"/>
                    <TextBlock x:Name="LowStockText" Text="أصناف تحت الحد الأدنى: 0" FontWeight="SemiBold" 
                              Foreground="#DC3545" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="PrintBtn" Content="🖨️ طباعة" Height="40" Width="120" Margin="0,0,10,0"
                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                            FontWeight="SemiBold" Click="PrintBtn_Click"/>
                    <Button x:Name="ExportExcelBtn" Content="📊 تصدير Excel" Height="40" Width="140" Margin="0,0,10,0"
                            Background="#28A745" Foreground="White" BorderThickness="0"
                            FontWeight="SemiBold" Click="ExportExcelBtn_Click"/>
                    <Button x:Name="ExportPdfBtn" Content="📄 تصدير PDF" Height="40" Width="130"
                            Background="#DC3545" Foreground="White" BorderThickness="0"
                            FontWeight="SemiBold" Click="ExportPdfBtn_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
