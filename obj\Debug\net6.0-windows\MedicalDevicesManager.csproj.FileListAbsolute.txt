D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\MedicalDevicesManager.exe
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\MedicalDevicesManager.deps.json
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\MedicalDevicesManager.runtimeconfig.json
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\MedicalDevicesManager.pdb
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\ClosedXML.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\ClosedXML.Parser.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\DocumentFormat.OpenXml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\DocumentFormat.OpenXml.Framework.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\EPPlus.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\EPPlus.System.Drawing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\ExcelNumberFormat.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.barcodes.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.bouncy-castle-connector.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.forms.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.io.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.kernel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.layout.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.pdfa.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.sign.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.styledxmlparser.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.svg.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\itext.commons.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Data.Sqlite.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.DotNet.PlatformAbstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Memory.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyModel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\RBush.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\SixLabors.Fonts.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\SQLitePCLRaw.batteries_v2.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\SQLitePCLRaw.core.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\System.IO.Packaging.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\System.Text.Encodings.Web.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\System.Text.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\alpine-arm\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\alpine-arm64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\alpine-x64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\win-arm\native\e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\win-x64\native\e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\win-x86\native\e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Debug\net6.0-windows\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.csproj.AssemblyReference.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MainWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditCustomerWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditDeviceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditInstallationWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditInventoryWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditMaintenanceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSaleWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditShipmentWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSparePartWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSupplierWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AdvancedInventorySettingsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\BackupWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\CreateBackupWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\DeviceDetailsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ExportWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\InventoryItemReportWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\InventorySelectionWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCategoriesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCitiesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCustomerTypesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageInventoryCategoriesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageRecipientsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageSerialNumbersWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageTechniciansWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\NotificationsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ReportsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SettingsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SmartSystemWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\App.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MainWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditCustomerWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditDeviceWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditInstallationWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditInventoryWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditMaintenanceWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSaleWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditShipmentWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSparePartWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AddEditSupplierWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\AdvancedInventorySettingsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\BackupWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\CreateBackupWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\DeviceDetailsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ExportWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\InventoryItemReportWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\InventorySelectionWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCategoriesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCitiesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageCustomerTypesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageInventoryCategoriesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageRecipientsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageSerialNumbersWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ManageTechniciansWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\NotificationsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\ReportsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SettingsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SmartSystemWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\App.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager_MarkupCompile.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.g.resources
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.GeneratedMSBuildEditorConfig.editorconfig
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.AssemblyInfoInputs.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.AssemblyInfo.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.csproj.CoreCompileInputs.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalD.3D21AE2B.Up2Date
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\refint\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.pdb
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\MedicalDevicesManager.genruntimeconfig.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\ref\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\DeviceSerialNumbersWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\DeviceSerialNumbersWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SelectDeviceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\SelectDeviceWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\MaintenanceDetailsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\Windows\MaintenanceDetailsWindow.g.cs
