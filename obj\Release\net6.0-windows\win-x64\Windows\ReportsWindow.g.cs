﻿#pragma checksum "..\..\..\..\..\Windows\ReportsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "95ACD788F93C79BEF53F5A9E86C0CD69784F29C8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// ReportsWindow
    /// </summary>
    public partial class ReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesReportBtn;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryReportBtn;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomersReportBtn;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaintenanceReportBtn;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FinancialReportBtn;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ComprehensiveReportBtn;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintReportBtn;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfBtn;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelBtn;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ReportScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Windows\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/reportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SalesReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 30 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.SalesReportBtn.Click += new System.Windows.RoutedEventHandler(this.SalesReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InventoryReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.InventoryReportBtn.Click += new System.Windows.RoutedEventHandler(this.InventoryReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CustomersReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.CustomersReportBtn.Click += new System.Windows.RoutedEventHandler(this.CustomersReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.MaintenanceReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.MaintenanceReportBtn.Click += new System.Windows.RoutedEventHandler(this.MaintenanceReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FinancialReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.FinancialReportBtn.Click += new System.Windows.RoutedEventHandler(this.FinancialReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ComprehensiveReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.ComprehensiveReportBtn.Click += new System.Windows.RoutedEventHandler(this.ComprehensiveReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PrintReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.PrintReportBtn.Click += new System.Windows.RoutedEventHandler(this.PrintReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportPdfBtn = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.ExportPdfBtn.Click += new System.Windows.RoutedEventHandler(this.ExportPdfBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ExportExcelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\..\Windows\ReportsWindow.xaml"
            this.ExportExcelBtn.Click += new System.Windows.RoutedEventHandler(this.ExportExcelBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ReportScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 11:
            this.ReportContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

