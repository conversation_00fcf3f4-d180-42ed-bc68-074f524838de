<Window x:Class="MedicalDevicesManager.Windows.ManageCategoriesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة فئات الأجهزة الطبية" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📋 إدارة فئات الأجهزة الطبية" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button x:Name="AddCategoryBtn" Content="➕ إضافة فئة جديدة" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AddCategoryBtn_Click"/>
            
            <Button x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="RefreshBtn_Click"/>
        </StackPanel>
        
        <!-- جدول الفئات -->
        <DataGrid Grid.Row="2" x:Name="CategoriesDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="الأيقونة" Binding="{Binding Icon}" Width="80"/>
                <DataGridTextColumn Header="اسم الفئة" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="300"/>
                <DataGridCheckBoxColumn Header="نشطة" Binding="{Binding IsActive}" Width="80"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✏️" Width="30" Height="25" 
                                        Background="#FFC107" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تعديل"
                                        Click="EditCategoryBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteCategoryBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج إضافة/تعديل الفئة -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                CornerRadius="5" Padding="20" Margin="0,20,0,0">

            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="الأيقونة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="1" x:Name="IconComboBox" Height="30" Margin="0,0,20,0">
                        <ComboBoxItem Content="🏥 مستشفى"/>
                        <ComboBoxItem Content="🔬 مختبر"/>
                        <ComboBoxItem Content="💉 حقن"/>
                        <ComboBoxItem Content="🩺 فحص"/>
                        <ComboBoxItem Content="📡 أشعة"/>
                        <ComboBoxItem Content="⚡ كهربائي"/>
                        <ComboBoxItem Content="🧪 كيميائي"/>
                        <ComboBoxItem Content="🦴 عظام"/>
                        <ComboBoxItem Content="❤️ قلب"/>
                        <ComboBoxItem Content="🧠 أعصاب"/>
                        <ComboBoxItem Content="👁️ عيون"/>
                        <ComboBoxItem Content="🦷 أسنان"/>
                    </ComboBox>

                    <TextBlock Grid.Column="2" Text="اسم الفئة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="3" x:Name="CategoryNameTextBox" Height="30" Margin="0,0,20,0"/>

                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="SaveCategoryBtn" Content="💾 حفظ" Width="80" Height="30"
                                Background="#28A745" Foreground="White" BorderThickness="0"
                                FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="SaveCategoryBtn_Click"/>
                        <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="80" Height="30"
                                Background="#DC3545" Foreground="White" BorderThickness="0"
                                FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                    </StackPanel>
                </Grid>

                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="الوصف:" VerticalAlignment="Top" Margin="0,5,10,0"/>
                    <TextBox Grid.Column="1" x:Name="CategoryDescriptionTextBox" Height="60"
                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>

                    <CheckBox Grid.Column="2" x:Name="IsActiveCheckBox" Content="نشطة"
                              VerticalAlignment="Center" Margin="20,0,0,0" IsChecked="True"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
