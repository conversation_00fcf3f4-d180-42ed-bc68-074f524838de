<Window x:Class="MedicalDevicesManager.Windows.SelectDeviceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار الجهاز الطبي" Height="600" Width="900"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize"
        FlowDirection="RightToLeft" FontFamily="Segoe UI">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختيار الجهاز الطبي للصيانة" 
                   FontSize="20" FontWeight="Bold" Foreground="#2C3E50" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- شريط البحث -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:Name="SearchTextBox" Grid.Column="0" Height="35" Padding="10,8" FontSize="14"
                     TextChanged="SearchTextBox_TextChanged"
                     ToolTip="ابحث بالاسم، الماركة، الموديل، أو الرقم التسلسلي"/>
            
            <Button Grid.Column="1" Width="100" Height="35" Margin="10,0,0,0"
                    Content="🔍 بحث" FontSize="12" FontWeight="SemiBold"
                    Background="#28A745" Foreground="White" BorderThickness="0"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- جدول الأجهزة -->
        <Border Grid.Row="2" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="5">
            <DataGrid x:Name="DevicesDataGrid" AutoGenerateColumns="False" 
                      CanUserAddRows="False" CanUserDeleteRows="False" 
                      IsReadOnly="True" SelectionMode="Single"
                      GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                      Background="White" AlternatingRowBackground="#F8F9FA"
                      MouseDoubleClick="DevicesDataGrid_MouseDoubleClick"
                      Loaded="DevicesDataGrid_Loaded">
                
                <DataGrid.Columns>
                    <!-- اسم الجهاز -->
                    <DataGridTextColumn Header="اسم الجهاز" Binding="{Binding Name}" Width="200"/>
                    
                    <!-- الماركة -->
                    <DataGridTextColumn Header="الماركة" Binding="{Binding Brand}" Width="120"/>
                    
                    <!-- الموديل -->
                    <DataGridTextColumn Header="الموديل" Binding="{Binding Model}" Width="120"/>
                    
                    <!-- الرقم التسلسلي -->
                    <DataGridTextColumn Header="الرقم التسلسلي" Binding="{Binding SerialNumber}" Width="150"/>
                    
                    <!-- الفئة -->
                    <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="120"/>
                    
                    <!-- الحالة -->
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                    
                    <!-- الموقع -->
                    <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SelectButton" Width="120" Height="40" Margin="0,0,15,0"
                    Content="✅ اختيار" FontSize="14" FontWeight="SemiBold"
                    Background="#007BFF" Foreground="White" BorderThickness="0"
                    Click="SelectButton_Click" IsEnabled="False"/>
            
            <Button x:Name="CancelButton" Width="120" Height="40"
                    Content="❌ إلغاء" FontSize="14" FontWeight="SemiBold"
                    Background="#6C757D" Foreground="White" BorderThickness="0"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
