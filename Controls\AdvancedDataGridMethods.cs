using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Documents;
using Microsoft.Win32;
using System.IO;
using System.Text;
using OfficeOpenXml;
using iText.Kernel.Pdf;
using iTextParagraph = iText.Layout.Element.Paragraph;
using iTextTable = iText.Layout.Element.Table;
using iText.Layout;
using iText.Layout.Element;

namespace MedicalDevicesManager.Controls
{
    /// <summary>
    /// الوظائف الأساسية للجدول المتقدم
    /// </summary>
    public partial class AdvancedDataGrid
    {
        #region Data Management

        /// <summary>
        /// تطبيق الفلاتر والبحث
        /// </summary>
        private void ApplyFilters()
        {
            if (_originalData == null) return;

            var filteredData = _originalData.AsEnumerable();

            // تطبيق البحث العام
            if (!string.IsNullOrWhiteSpace(_globalSearchBox?.Text))
            {
                var searchTerm = _globalSearchBox.Text.ToLower();
                filteredData = filteredData.Where(item => 
                    item.GetType().GetProperties()
                        .Any(prop => prop.GetValue(item)?.ToString()?.ToLower().Contains(searchTerm) == true));
            }

            // تطبيق الفلاتر المتقدمة
            foreach (var filter in _activeFilters)
            {
                // تطبيق الفلتر حسب النوع
                filteredData = ApplySpecificFilter(filteredData, filter.Key, filter.Value);
            }

            _filteredData = new ObservableCollection<object>(filteredData);
            CalculatePagination();
            LoadCurrentPage();
            UpdateRecordsInfo();
        }

        /// <summary>
        /// تطبيق فلتر محدد
        /// </summary>
        private IEnumerable<object> ApplySpecificFilter(IEnumerable<object> data, string propertyName, object filterValue)
        {
            if (filterValue == null) return data;

            return data.Where(item =>
            {
                var property = item.GetType().GetProperty(propertyName);
                if (property == null) return true;

                var value = property.GetValue(item);
                if (value == null) return false;

                // فلترة حسب نوع البيانات
                if (filterValue is string stringFilter && !string.IsNullOrEmpty(stringFilter))
                {
                    return value.ToString().ToLower().Contains(stringFilter.ToLower());
                }
                else if (filterValue is DateTime dateFilter)
                {
                    if (value is DateTime dateValue)
                    {
                        return dateValue.Date == dateFilter.Date;
                    }
                }
                else if (filterValue is decimal decimalFilter)
                {
                    if (value is decimal decimalValue)
                    {
                        return decimalValue == decimalFilter;
                    }
                }

                return true;
            });
        }

        /// <summary>
        /// حساب معلومات التنقل بين الصفحات
        /// </summary>
        private void CalculatePagination()
        {
            if (!EnablePagination || _filteredData == null)
            {
                _totalPages = 1;
                _totalRecords = _filteredData?.Count ?? 0;
                return;
            }

            _totalRecords = _filteredData.Count;
            _totalPages = (int)Math.Ceiling((double)_totalRecords / PageSize);
            
            if (_totalPages == 0) _totalPages = 1;
            if (_currentPage > _totalPages) _currentPage = _totalPages;

            UpdatePaginationControls();
        }

        /// <summary>
        /// تحميل الصفحة الحالية
        /// </summary>
        private void LoadCurrentPage()
        {
            if (_filteredData == null) return;

            IEnumerable<object> pageData;

            if (EnablePagination)
            {
                var skip = (_currentPage - 1) * PageSize;
                pageData = _filteredData.Skip(skip).Take(PageSize);
            }
            else
            {
                pageData = _filteredData;
            }

            _mainDataGrid.ItemsSource = pageData;
            UpdatePaginationControls();
        }

        /// <summary>
        /// تحديث عناصر التحكم في التنقل
        /// </summary>
        private void UpdatePaginationControls()
        {
            if (!EnablePagination) return;

            _currentPageTextBox.Text = _currentPage.ToString();
            _totalPagesTextBlock.Text = _totalPages.ToString();

            _firstPageButton.IsEnabled = _currentPage > 1;
            _previousPageButton.IsEnabled = _currentPage > 1;
            _nextPageButton.IsEnabled = _currentPage < _totalPages;
            _lastPageButton.IsEnabled = _currentPage < _totalPages;
        }

        /// <summary>
        /// تحديث معلومات السجلات
        /// </summary>
        private void UpdateRecordsInfo()
        {
            if (_recordsInfoTextBlock == null) return;

            var startRecord = EnablePagination ? ((_currentPage - 1) * PageSize) + 1 : 1;
            var endRecord = EnablePagination ? Math.Min(_currentPage * PageSize, _totalRecords) : _totalRecords;

            _recordsInfoTextBlock.Text = $"عرض {startRecord}-{endRecord} من {_totalRecords} سجل";
        }

        #endregion

        #region Export Functions

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    using (var package = new ExcelPackage())
                    {
                        var worksheet = package.Workbook.Worksheets.Add("البيانات");
                        
                        // إضافة العناوين
                        for (int i = 0; i < _mainDataGrid.Columns.Count; i++)
                        {
                            worksheet.Cells[1, i + 1].Value = _mainDataGrid.Columns[i].Header?.ToString();
                            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        }

                        // إضافة البيانات
                        var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
                        if (data != null)
                        {
                            for (int row = 0; row < data.Count; row++)
                            {
                                var item = data[row];
                                for (int col = 0; col < _mainDataGrid.Columns.Count; col++)
                                {
                                    var column = _mainDataGrid.Columns[col];
                                    if (column is DataGridTextColumn textColumn && textColumn.Binding is Binding binding)
                                    {
                                        var property = item.GetType().GetProperty(binding.Path.Path);
                                        var value = property?.GetValue(item);
                                        worksheet.Cells[row + 2, col + 1].Value = value;
                                    }
                                }
                            }
                        }

                        // تنسيق الجدول
                        worksheet.Cells.AutoFitColumns();
                        
                        // حفظ الملف
                        package.SaveAs(new FileInfo(saveDialog.FileName));
                    }

                    MessageBox.Show("تم تصدير البيانات بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private void ExportToPdf()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"Report_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    using (var writer = new PdfWriter(saveDialog.FileName))
                    using (var pdf = new PdfDocument(writer))
                    {
                        var document = new Document(pdf);

                        try
                        {
                            // محاولة إنشاء خط يدعم العربية
                            var fontProgram = iText.IO.Font.FontProgramFactory.CreateFont(iText.IO.Font.Constants.StandardFonts.HELVETICA);
                            var font = iText.Kernel.Font.PdfFontFactory.CreateFont(fontProgram, iText.IO.Font.PdfEncodings.IDENTITY_H);

                            // إضافة عنوان
                            document.Add(new iTextParagraph("Data Report - تقرير البيانات")
                                .SetFont(font)
                                .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER)
                                .SetFontSize(18));

                            // إنشاء الجدول
                            if (_mainDataGrid.Columns.Count > 0)
                            {
                                var table = new iTextTable(_mainDataGrid.Columns.Count);
                                table.SetWidth(iText.Layout.Properties.UnitValue.CreatePercentValue(100));

                                // إضافة العناوين
                                foreach (var column in _mainDataGrid.Columns)
                                {
                                    var headerText = column.Header?.ToString() ?? "";
                                    table.AddHeaderCell(new Cell().Add(new iTextParagraph(headerText).SetFont(font)));
                                }

                                // إضافة البيانات
                                var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
                                if (data != null && data.Any())
                                {
                                    foreach (var item in data)
                                    {
                                        foreach (var column in _mainDataGrid.Columns)
                                        {
                                            string cellValue = "";
                                            try
                                            {
                                                if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                                                {
                                                    var property = item.GetType().GetProperty(binding.Path.Path);
                                                    cellValue = property?.GetValue(item)?.ToString() ?? "";
                                                }
                                            }
                                            catch
                                            {
                                                cellValue = "";
                                            }

                                            table.AddCell(new Cell().Add(new iTextParagraph(cellValue).SetFont(font)));
                                        }
                                    }
                                }

                                document.Add(table);
                            }
                            else
                            {
                                document.Add(new iTextParagraph("No data to display").SetFont(font));
                            }
                        }
                        catch (Exception)
                        {
                            // إذا فشل إنشاء الخط، استخدم الخط الافتراضي
                            document.Add(new iTextParagraph("Data Report")
                                .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER)
                                .SetFontSize(18));

                            if (_mainDataGrid.Columns.Count > 0)
                            {
                                var table = new iTextTable(_mainDataGrid.Columns.Count);
                                table.SetWidth(iText.Layout.Properties.UnitValue.CreatePercentValue(100));

                                foreach (var column in _mainDataGrid.Columns)
                                {
                                    var headerText = column.Header?.ToString() ?? "";
                                    table.AddHeaderCell(new Cell().Add(new iTextParagraph(headerText)));
                                }

                                var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
                                if (data != null && data.Any())
                                {
                                    foreach (var item in data)
                                    {
                                        foreach (var column in _mainDataGrid.Columns)
                                        {
                                            string cellValue = "";
                                            try
                                            {
                                                if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                                                {
                                                    var property = item.GetType().GetProperty(binding.Path.Path);
                                                    cellValue = property?.GetValue(item)?.ToString() ?? "";
                                                }
                                            }
                                            catch
                                            {
                                                cellValue = "";
                                            }

                                            table.AddCell(new Cell().Add(new iTextParagraph(cellValue)));
                                        }
                                    }
                                }

                                document.Add(table);
                            }
                        }

                        document.Close();
                    }

                    MessageBox.Show("تم تصدير البيانات بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private void ExportToCsv()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files|*.csv",
                    DefaultExt = "csv",
                    FileName = $"تقرير_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var csv = new StringBuilder();

                    // إضافة العناوين
                    var headers = _mainDataGrid.Columns.Select(c => c.Header?.ToString() ?? "").ToArray();
                    csv.AppendLine(string.Join(",", headers));

                    // إضافة البيانات
                    var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
                    if (data != null)
                    {
                        foreach (var item in data)
                        {
                            var values = new List<string>();
                            foreach (var column in _mainDataGrid.Columns)
                            {
                                if (column is DataGridTextColumn textColumn && textColumn.Binding is Binding binding)
                                {
                                    var property = item.GetType().GetProperty(binding.Path.Path);
                                    var value = property?.GetValue(item)?.ToString() ?? "";
                                    values.Add($"\"{value}\"");
                                }
                                else
                                {
                                    values.Add("\"\"");
                                }
                            }
                            csv.AppendLine(string.Join(",", values));
                        }
                    }

                    File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);
                    MessageBox.Show("تم تصدير البيانات بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة البيانات
        /// </summary>
        private void PrintData()
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    var flowDocument = CreatePrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)flowDocument).DocumentPaginator, "تقرير البيانات");
                    
                    MessageBox.Show("تم إرسال البيانات للطباعة!", "نجحت الطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند للطباعة
        /// </summary>
        private System.Windows.Documents.FlowDocument CreatePrintDocument()
        {
            var document = new System.Windows.Documents.FlowDocument();
            
            // إضافة عنوان
            var title = new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run("تقرير البيانات"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center
            };
            document.Blocks.Add(title);

            // إضافة تاريخ التقرير
            var date = new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Center
            };
            document.Blocks.Add(date);

            // إضافة جدول البيانات
            var table = new System.Windows.Documents.Table();
            
            // إضافة أعمدة
            foreach (var column in _mainDataGrid.Columns)
            {
                table.Columns.Add(new System.Windows.Documents.TableColumn());
            }

            // إضافة مجموعة الصفوف
            var rowGroup = new System.Windows.Documents.TableRowGroup();

            // إضافة صف العناوين
            var headerRow = new System.Windows.Documents.TableRow();
            foreach (var column in _mainDataGrid.Columns)
            {
                var cell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run(column.Header?.ToString() ?? "")))
                {
                    FontWeight = FontWeights.Bold,
                    Background = Brushes.LightGray
                };
                headerRow.Cells.Add(cell);
            }
            rowGroup.Rows.Add(headerRow);

            // إضافة صفوف البيانات
            var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
            if (data != null)
            {
                foreach (var item in data)
                {
                    var dataRow = new System.Windows.Documents.TableRow();
                    foreach (var column in _mainDataGrid.Columns)
                    {
                        string cellValue = "";
                        if (column is DataGridTextColumn textColumn && textColumn.Binding is Binding binding)
                        {
                            var property = item.GetType().GetProperty(binding.Path.Path);
                            cellValue = property?.GetValue(item)?.ToString() ?? "";
                        }
                        
                        var cell = new System.Windows.Documents.TableCell(new System.Windows.Documents.Paragraph(new System.Windows.Documents.Run(cellValue)));
                        dataRow.Cells.Add(cell);
                    }
                    rowGroup.Rows.Add(dataRow);
                }
            }

            table.RowGroups.Add(rowGroup);
            document.Blocks.Add(table);

            return document;
        }

        #endregion

        #region Advanced Features

        /// <summary>
        /// عرض نافذة الفلترة المتقدمة المحسنة
        /// </summary>
        private void ShowAdvancedFilterDialogEnhanced()
        {
            var searchFields = new List<SearchField>();

            // إنشاء حقول البحث من أعمدة الجدول
            foreach (var column in _mainDataGrid.Columns)
            {
                if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                {
                    var field = new SearchField
                    {
                        Name = binding.Path.Path,
                        DisplayName = column.Header?.ToString() ?? binding.Path.Path,
                        Type = DetermineFieldType(binding.Path.Path)
                    };

                    if (field.Type == SearchFieldType.Dropdown)
                    {
                        field.Options = GetDistinctValues(binding.Path.Path);
                    }

                    searchFields.Add(field);
                }
            }

            var dialog = new AdvancedSearchDialog(searchFields, _activeFilters);
            if (dialog.ShowDialog() == true)
            {
                _activeFilters = dialog.GetFilters();
                ApplyFilters();
            }
        }

        /// <summary>
        /// تحديد نوع الحقل من اسمه
        /// </summary>
        private SearchFieldType DetermineFieldType(string fieldName)
        {
            var lowerName = fieldName.ToLower();

            if (lowerName.Contains("date") || lowerName.Contains("تاريخ"))
                return SearchFieldType.Date;
            else if (lowerName.Contains("price") || lowerName.Contains("amount") || lowerName.Contains("cost") ||
                     lowerName.Contains("سعر") || lowerName.Contains("مبلغ") || lowerName.Contains("تكلفة"))
                return SearchFieldType.Number;
            else if (lowerName.Contains("status") || lowerName.Contains("حالة") || lowerName.Contains("category") || lowerName.Contains("فئة"))
                return SearchFieldType.Dropdown;
            else if (lowerName.Contains("isactive") || lowerName.Contains("isenabled") || lowerName.Contains("نشط"))
                return SearchFieldType.Boolean;
            else
                return SearchFieldType.Text;
        }

        /// <summary>
        /// الحصول على القيم المميزة لحقل معين
        /// </summary>
        private List<string> GetDistinctValues(string fieldName)
        {
            if (_originalData == null) return new List<string>();

            var values = new HashSet<string>();

            foreach (var item in _originalData)
            {
                var property = item.GetType().GetProperty(fieldName);
                var value = property?.GetValue(item)?.ToString();
                if (!string.IsNullOrEmpty(value))
                {
                    values.Add(value);
                }
            }

            return values.OrderBy(v => v).ToList();
        }

        /// <summary>
        /// عرض نافذة تخصيص الأعمدة المحسنة
        /// </summary>
        private void ShowColumnCustomizationDialogEnhanced()
        {
            var dialog = new ColumnCustomizationDialog(_mainDataGrid.Columns.ToList());
            if (dialog.ShowDialog() == true)
            {
                dialog.ApplyChanges();
            }
        }

        #endregion

        #region Quick Actions

        /// <summary>
        /// تصدير سريع إلى Excel
        /// </summary>
        public void QuickExportToExcel(string fileName = null)
        {
            try
            {
                fileName = fileName ?? $"تقرير_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("البيانات");

                    // إضافة العناوين
                    for (int i = 0; i < _mainDataGrid.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = _mainDataGrid.Columns[i].Header?.ToString();
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    // إضافة البيانات
                    var data = _mainDataGrid.ItemsSource?.Cast<object>().ToList();
                    if (data != null)
                    {
                        for (int row = 0; row < data.Count; row++)
                        {
                            var item = data[row];
                            for (int col = 0; col < _mainDataGrid.Columns.Count; col++)
                            {
                                var column = _mainDataGrid.Columns[col];
                                if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                                {
                                    var property = item.GetType().GetProperty(binding.Path.Path);
                                    var value = property?.GetValue(item);
                                    worksheet.Cells[row + 2, col + 1].Value = value;
                                }
                            }
                        }
                    }

                    // تنسيق الجدول
                    worksheet.Cells.AutoFitColumns();

                    // حفظ الملف
                    package.SaveAs(new FileInfo(filePath));
                }

                MessageBox.Show($"تم تصدير البيانات إلى:\n{filePath}", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة سريعة
        /// </summary>
        public void QuickPrint()
        {
            try
            {
                var printDialog = new PrintDialog();
                var flowDocument = CreatePrintDocument();
                printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)flowDocument).DocumentPaginator, "تقرير البيانات");

                MessageBox.Show("تم إرسال البيانات للطباعة!", "نجحت الطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// نسخ البيانات المحددة إلى الحافظة
        /// </summary>
        public void CopySelectedToClipboard()
        {
            try
            {
                var selectedItems = GetSelectedItems();
                if (!selectedItems.Any())
                {
                    MessageBox.Show("لم يتم تحديد أي عناصر", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var csv = new StringBuilder();

                // إضافة العناوين
                var headers = _mainDataGrid.Columns.Select(c => c.Header?.ToString() ?? "").ToArray();
                csv.AppendLine(string.Join("\t", headers));

                // إضافة البيانات المحددة
                foreach (var item in selectedItems)
                {
                    var values = new List<string>();
                    foreach (var column in _mainDataGrid.Columns)
                    {
                        if (column is DataGridTextColumn textColumn && textColumn.Binding is System.Windows.Data.Binding binding)
                        {
                            var property = item.GetType().GetProperty(binding.Path.Path);
                            var value = property?.GetValue(item)?.ToString() ?? "";
                            values.Add(value);
                        }
                        else
                        {
                            values.Add("");
                        }
                    }
                    csv.AppendLine(string.Join("\t", values));
                }

                Clipboard.SetText(csv.ToString());
                MessageBox.Show($"تم نسخ {selectedItems.Count} عنصر إلى الحافظة", "نجح النسخ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// الحصول على إحصائيات البيانات
        /// </summary>
        public DataStatistics GetDataStatistics()
        {
            var stats = new DataStatistics
            {
                TotalRecords = _originalData?.Count ?? 0,
                FilteredRecords = _filteredData?.Count ?? 0,
                SelectedRecords = GetSelectedItems().Count,
                CurrentPageRecords = _mainDataGrid.ItemsSource?.Cast<object>().Count() ?? 0
            };

            return stats;
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات البيانات
    /// </summary>
    public class DataStatistics
    {
        public int TotalRecords { get; set; }
        public int FilteredRecords { get; set; }
        public int SelectedRecords { get; set; }
        public int CurrentPageRecords { get; set; }
    }
}
