<Window x:Class="MedicalDevicesManager.Windows.ManageTechniciansWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة أسماء الفنيين" Height="600" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🔧 إدارة أسماء الفنيين" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button x:Name="AddTechnicianBtn" Content="➕ إضافة فني جديد" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="AddTechnicianBtn_Click"/>
            
            <Button x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="RefreshBtn_Click"/>
        </StackPanel>
        
        <!-- جدول الفنيين -->
        <DataGrid Grid.Row="2" x:Name="TechniciansDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم الفني" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="التخصص" Binding="{Binding Specialization}" Width="200"/>
                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="80"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✏️" Width="30" Height="25" 
                                        Background="#FFC107" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تعديل"
                                        Click="EditTechnicianBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteTechnicianBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- نموذج إضافة/تعديل الفني -->
        <Border Grid.Row="3" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,20,0,0">
            
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="اسم الفني:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" x:Name="TechnicianNameTextBox" Height="30" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="التخصص:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="3" x:Name="SpecializationComboBox" Height="30" Margin="0,0,20,0" IsEditable="True">
                        <ComboBoxItem Content="صيانة عامة"/>
                        <ComboBoxItem Content="أجهزة طبية"/>
                        <ComboBoxItem Content="أجهزة تشخيص"/>
                        <ComboBoxItem Content="أجهزة جراحية"/>
                        <ComboBoxItem Content="أجهزة مختبر"/>
                        <ComboBoxItem Content="أجهزة أشعة"/>
                        <ComboBoxItem Content="أجهزة قلب"/>
                        <ComboBoxItem Content="أجهزة تنفس"/>
                        <ComboBoxItem Content="أجهزة كلى"/>
                        <ComboBoxItem Content="متخصص"/>
                    </ComboBox>
                    
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="SaveTechnicianBtn" Content="💾 حفظ" Width="80" Height="30" 
                                Background="#28A745" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Margin="0,0,5,0" Click="SaveTechnicianBtn_Click"/>
                        <Button x:Name="CancelBtn" Content="❌ إلغاء" Width="80" Height="30" 
                                Background="#DC3545" Foreground="White" BorderThickness="0" 
                                FontSize="12" FontWeight="SemiBold" Click="CancelBtn_Click"/>
                    </StackPanel>
                </Grid>
                
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="رقم الهاتف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" x:Name="PhoneTextBox" Height="30" Margin="0,0,20,0"/>
                    
                    <CheckBox Grid.Column="2" x:Name="IsActiveCheckBox" Content="نشط" 
                              VerticalAlignment="Center" IsChecked="True"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
