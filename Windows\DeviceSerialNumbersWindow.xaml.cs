using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class DeviceSerialNumbersWindow : Window
    {
        private readonly int _deviceId;
        private readonly string _deviceName;
        private ObservableCollection<DeviceSerialNumber> _serialNumbers;

        public DeviceSerialNumbersWindow(int deviceId, string deviceName)
        {
            InitializeComponent();
            _deviceId = deviceId;
            _deviceName = deviceName;
            
            WindowTitle.Text = $"إدارة الأرقام التسلسلية - {deviceName}";
            
            _serialNumbers = new ObservableCollection<DeviceSerialNumber>();
            SerialNumbersDataGrid.ItemsSource = _serialNumbers;
            
            Loaded += DeviceSerialNumbersWindow_Loaded;
        }

        private async void DeviceSerialNumbersWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSerialNumbersAsync();
        }

        private async Task LoadSerialNumbersAsync()
        {
            try
            {
                var serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == _deviceId && s.IsActive)
                    .OrderByDescending(s => s.CreatedDate)
                    .ToListAsync();

                _serialNumbers.Clear();
                foreach (var serial in serialNumbers)
                {
                    _serialNumbers.Add(serial);
                }

                UpdateCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                // التحقق من عدم تكرار الرقم التسلسلي
                var existingSerial = await App.DatabaseContext.DeviceSerialNumbers
                    .FirstOrDefaultAsync(s => s.DeviceId == _deviceId && 
                                            s.SerialNumber.ToLower() == SerialNumberTextBox.Text.Trim().ToLower() && 
                                            s.IsActive);

                if (existingSerial != null)
                {
                    MessageBox.Show("هذا الرقم التسلسلي موجود بالفعل لهذا الجهاز!", 
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    SerialNumberTextBox.Focus();
                    return;
                }

                // إنشاء رقم تسلسلي جديد
                var newSerial = new DeviceSerialNumber
                {
                    DeviceId = _deviceId,
                    SerialNumber = SerialNumberTextBox.Text.Trim(),
                    ComponentName = ComponentNameTextBox.Text.Trim(),
                    Notes = NotesTextBox.Text.Trim(),
                    ComponentType = "مكون أساسي", // قيمة افتراضية
                    Status = "نشط",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastUpdated = DateTime.Now
                };

                App.DatabaseContext.DeviceSerialNumbers.Add(newSerial);
                await App.DatabaseContext.SaveChangesAsync();

                // إضافة إلى المجموعة المحلية
                _serialNumbers.Insert(0, newSerial);
                UpdateCount();

                // مسح النموذج
                ClearForm();

                MessageBox.Show("تم إضافة الرقم التسلسلي بنجاح!", 
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الرقم التسلسلي: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(SerialNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم التسلسلي", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ComponentNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكون", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                ComponentNameTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            SerialNumberTextBox.Clear();
            ComponentNameTextBox.Clear();
            NotesTextBox.Clear();
            SerialNumberTextBox.Focus();
        }

        private void ClearBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private async void DeleteMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (SerialNumbersDataGrid.SelectedItem is DeviceSerialNumber selectedSerial)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف الرقم التسلسلي '{selectedSerial.SerialNumber}' للمكون '{selectedSerial.ComponentName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // حذف منطقي
                        selectedSerial.IsActive = false;
                        selectedSerial.LastUpdated = DateTime.Now;

                        await App.DatabaseContext.SaveChangesAsync();

                        // إزالة من المجموعة المحلية
                        _serialNumbers.Remove(selectedSerial);
                        UpdateCount();

                        MessageBox.Show("تم حذف الرقم التسلسلي بنجاح!", 
                            "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الرقم التسلسلي: {ex.Message}", 
                            "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رقم تسلسلي للحذف", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            await LoadSerialNumbersAsync();
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void UpdateCount()
        {
            CountTextBlock.Text = $"العدد: {_serialNumbers.Count}";
        }
    }
}
