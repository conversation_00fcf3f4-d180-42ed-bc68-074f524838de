﻿#pragma checksum "..\..\..\..\Windows\ExportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "20B22EF93C3FE130B32FA15B5092939E67B2725C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// ExportWindow
    /// </summary>
    public partial class ExportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 31 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDevicesBtn;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportInventoryBtn;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportSalesBtn;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ExportsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DataTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExportTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomExportBtn;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ExportProgressBar;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExportsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessfulExportsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Windows\ExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/exportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ExportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ExportDevicesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Windows\ExportWindow.xaml"
            this.ExportDevicesBtn.Click += new System.Windows.RoutedEventHandler(this.ExportDevicesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportInventoryBtn = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\Windows\ExportWindow.xaml"
            this.ExportInventoryBtn.Click += new System.Windows.RoutedEventHandler(this.ExportInventoryBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportSalesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\Windows\ExportWindow.xaml"
            this.ExportSalesBtn.Click += new System.Windows.RoutedEventHandler(this.ExportSalesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\Windows\ExportWindow.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ExportsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 56 "..\..\..\..\Windows\ExportWindow.xaml"
            this.ExportsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExportsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DataTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.ExportTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.FileNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 14:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 15:
            this.CustomExportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\..\Windows\ExportWindow.xaml"
            this.CustomExportBtn.Click += new System.Windows.RoutedEventHandler(this.CustomExportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ExportProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 17:
            this.TotalExportsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.SuccessfulExportsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.TotalSizeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 102 "..\..\..\..\Windows\ExportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFileBtn_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 106 "..\..\..\..\Windows\ExportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFolderBtn_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 110 "..\..\..\..\Windows\ExportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteExportBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

