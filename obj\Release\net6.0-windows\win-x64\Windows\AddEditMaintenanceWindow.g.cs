﻿#pragma checksum "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "343C0B7EE21268332EAF73E2DD24C9CB16825C78"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditMaintenanceWindow
    /// </summary>
    public partial class AddEditMaintenanceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SelectedDeviceTextBox;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectDeviceButton;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MaintenanceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker MaintenanceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NextMaintenanceDatePanel;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker NextMaintenanceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostTextBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TechnicianComboBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageTechniciansBtn;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SupplierCompanyTextBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReportBookletPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseReportBtn;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewReportBtn;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveReportBtn;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DocumentsTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDocumentBtn;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveDocumentBtn;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdatedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditmaintenancewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SelectedDeviceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.SelectDeviceButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.SelectDeviceButton.Click += new System.Windows.RoutedEventHandler(this.SelectDeviceButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.MaintenanceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.MaintenanceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 68 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.StatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.NextMaintenanceDatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.NextMaintenanceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.CostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TechnicianComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.ManageTechniciansBtn = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.ManageTechniciansBtn.Click += new System.Windows.RoutedEventHandler(this.ManageTechniciansBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SupplierCompanyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ReportBookletPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.BrowseReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.BrowseReportBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ViewReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.ViewReportBtn.Click += new System.Windows.RoutedEventHandler(this.ViewReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RemoveReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.RemoveReportBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DocumentsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.AddDocumentBtn = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.AddDocumentBtn.Click += new System.Windows.RoutedEventHandler(this.AddDocumentBtn_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.RemoveDocumentBtn = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.RemoveDocumentBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveDocumentBtn_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.DeviceInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.LastUpdatedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\..\Windows\AddEditMaintenanceWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

