using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageInventoryCategoriesWindow : Window
    {
        private InventoryCategory _editingCategory = null;
        
        public ManageInventoryCategoriesWindow()
        {
            InitializeComponent();
            LoadCategoriesAsync();
        }
        
        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.InventoryCategories
                    .OrderBy(c => c.Name)
                    .ToListAsync();
                    
                CategoriesDataGrid.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingCategory = null;
            CategoryNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadCategoriesAsync();
            ClearForm();
        }
        
        private void EditCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var category = button?.DataContext as InventoryCategory;
            
            if (category != null)
            {
                _editingCategory = category;
                LoadCategoryToForm(category);
            }
        }
        
        private async void DeleteCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var category = button?.DataContext as InventoryCategory;
            
            if (category != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفئة '{category.Name}'؟\n\nسيتم تحويل جميع عناصر المخزون في هذه الفئة إلى 'غير محدد'.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // تحديث عناصر المخزون التي تستخدم هذه الفئة
                        var itemsWithCategory = await App.DatabaseContext.InventoryItems
                            .Where(i => i.Category == category.Name)
                            .ToListAsync();
                            
                        foreach (var item in itemsWithCategory)
                        {
                            item.Category = "غير محدد";
                        }
                        
                        App.DatabaseContext.InventoryCategories.Remove(category);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الفئة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadCategoriesAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingCategory == null)
                {
                    // إضافة فئة جديدة
                    var newCategory = new InventoryCategory
                    {
                        Name = CategoryNameTextBox.Text.Trim(),
                        Description = CategoryDescriptionTextBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.InventoryCategories.Add(newCategory);
                }
                else
                {
                    // تعديل فئة موجودة
                    var oldName = _editingCategory.Name;
                    
                    _editingCategory.Name = CategoryNameTextBox.Text.Trim();
                    _editingCategory.Description = CategoryDescriptionTextBox.Text.Trim();
                    _editingCategory.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingCategory.LastUpdated = DateTime.Now;
                    
                    // تحديث عناصر المخزون التي تستخدم هذه الفئة
                    if (oldName != _editingCategory.Name)
                    {
                        var itemsWithCategory = await App.DatabaseContext.InventoryItems
                            .Where(i => i.Category == oldName)
                            .ToListAsync();
                            
                        foreach (var item in itemsWithCategory)
                        {
                            item.Category = _editingCategory.Name;
                        }
                    }
                    
                    App.DatabaseContext.InventoryCategories.Update(_editingCategory);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingCategory == null ? "تم إضافة الفئة بنجاح!" : "تم تحديث الفئة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadCategoriesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadCategoryToForm(InventoryCategory category)
        {
            CategoryNameTextBox.Text = category.Name;
            CategoryDescriptionTextBox.Text = category.Description;
            IsActiveCheckBox.IsChecked = category.IsActive;
        }
        
        private void ClearForm()
        {
            _editingCategory = null;
            CategoryNameTextBox.Text = "";
            CategoryDescriptionTextBox.Text = "";
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(CategoryNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryNameTextBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
