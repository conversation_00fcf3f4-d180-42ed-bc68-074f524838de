using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class SelectDeviceWindow : Window
    {
        private List<MedicalDevice> _allDevices;
        private List<MedicalDevice> _filteredDevices;
        public MedicalDevice SelectedDevice { get; private set; }

        public SelectDeviceWindow()
        {
            InitializeComponent();
            LoadDevicesAsync();
        }

        private async void LoadDevicesAsync()
        {
            try
            {
                // تحميل جميع الأجهزة من قاعدة البيانات
                _allDevices = await App.DatabaseContext.MedicalDevices
                    .Where(d => !string.IsNullOrEmpty(d.SerialNumber)) // فقط الأجهزة التي لها رقم تسلسلي
                    .OrderBy(d => d.Name)
                    .ThenBy(d => d.SerialNumber)
                    .ToListAsync();

                _filteredDevices = new List<MedicalDevice>(_allDevices);
                DevicesDataGrid.ItemsSource = _filteredDevices;

                System.Diagnostics.Debug.WriteLine($"تم تحميل {_allDevices.Count} جهاز");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الأجهزة: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterDevices();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            FilterDevices();
        }

        private void FilterDevices()
        {
            try
            {
                if (_allDevices == null) return;

                string searchText = SearchTextBox.Text?.Trim().ToLower() ?? "";

                if (string.IsNullOrEmpty(searchText))
                {
                    _filteredDevices = new List<MedicalDevice>(_allDevices);
                }
                else
                {
                    _filteredDevices = _allDevices.Where(d =>
                        (!string.IsNullOrEmpty(d.Name) && d.Name.ToLower().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(d.Brand) && d.Brand.ToLower().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(d.Model) && d.Model.ToLower().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(d.SerialNumber) && d.SerialNumber.ToLower().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(d.Category) && d.Category.ToLower().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(d.Location) && d.Location.ToLower().Contains(searchText))
                    ).ToList();
                }

                DevicesDataGrid.ItemsSource = _filteredDevices;
                System.Diagnostics.Debug.WriteLine($"تم العثور على {_filteredDevices.Count} جهاز");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
            }
        }

        private void DevicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectButton.IsEnabled = DevicesDataGrid.SelectedItem != null;
        }

        // إضافة event handler للتحديد
        private void DevicesDataGrid_Loaded(object sender, RoutedEventArgs e)
        {
            DevicesDataGrid.SelectionChanged += DevicesDataGrid_SelectionChanged;
        }

        private void DevicesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (DevicesDataGrid.SelectedItem is MedicalDevice device)
            {
                SelectedDevice = device;
                DialogResult = true;
                Close();
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (DevicesDataGrid.SelectedItem is MedicalDevice device)
            {
                SelectedDevice = device;
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى اختيار جهاز من القائمة", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
