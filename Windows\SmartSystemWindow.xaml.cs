using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MedicalDevicesManager.Services;

namespace MedicalDevicesManager.Windows
{
    public partial class SmartSystemWindow : Window
    {
        private SmartAlertService _alertService;
        private SmartSearchService _searchService;
        private LiveStatisticsService _statisticsService;
        private List<SmartAlert> _currentAlerts;
        private List<SearchResult> _currentSearchResults;

        public SmartSystemWindow()
        {
            InitializeComponent();
            InitializeServices();
            _ = LoadDataAsync();
        }

        private void InitializeServices()
        {
            _alertService = new SmartAlertService(App.DatabaseContext);
            _searchService = new SmartSearchService(App.DatabaseContext);
            _statisticsService = new LiveStatisticsService(App.DatabaseContext);
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // تحميل الإحصائيات
                await LoadStatisticsAsync();

                // تحميل التنبيهات
                await LoadAlertsAsync();

                // بناء فهرس البحث إذا لم يكن موجوداً
                await _searchService.BuildSearchIndexAsync();

                // تعيين القيم الافتراضية للفلاتر
                AlertTypeFilterComboBox.SelectedIndex = 0;
                AlertPriorityFilterComboBox.SelectedIndex = 0;
                ItemTypeFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تحميل الإحصائيات
        private async Task LoadStatisticsAsync()
        {
            try
            {
                // حساب الإحصائيات الجديدة
                await _statisticsService.CalculateAllStatisticsAsync();

                // تحميل الإحصائيات
                var statistics = await _statisticsService.GetStatisticsAsync();

                // عرض إحصائيات المبيعات
                DisplayStatistics(SalesStatsGrid, statistics.Where(s => s.StatType == "sales").ToList(), "💰");

                // عرض إحصائيات المخزون
                DisplayStatistics(InventoryStatsGrid, statistics.Where(s => s.StatType == "inventory").ToList(), "📦");

                // عرض إحصائيات الأجهزة
                DisplayStatistics(DevicesStatsGrid, statistics.Where(s => s.StatType == "devices").ToList(), "🏥");

                // عرض إحصائيات العملاء
                DisplayStatistics(CustomersStatsGrid, statistics.Where(s => s.StatType == "customers").ToList(), "👥");

                // عرض الإحصائيات المالية
                DisplayStatistics(FinancialStatsGrid, statistics.Where(s => s.StatType == "financial").ToList(), "💳");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // عرض الإحصائيات في الشبكة
        private void DisplayStatistics(Grid grid, List<LiveStatistics> stats, string icon)
        {
            grid.Children.Clear();

            for (int i = 0; i < stats.Count && i < 4; i++)
            {
                var stat = stats[i];
                var card = CreateStatCard(stat, icon);
                Grid.SetColumn(card, i);
                grid.Children.Add(card);
            }
        }

        // إنشاء بطاقة إحصائية
        private Border CreateStatCard(LiveStatistics stat, string icon)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(15),
                Margin = new Thickness(5)
            };

            var stackPanel = new StackPanel();

            // الأيقونة والعنوان
            var titlePanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            
            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 20,
                Margin = new Thickness(0, 0, 10, 0)
            };

            var titleText = new TextBlock
            {
                Text = GetStatDisplayName(stat.StatName),
                FontWeight = FontWeights.SemiBold,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87))
            };

            titlePanel.Children.Add(iconText);
            titlePanel.Children.Add(titleText);

            // القيمة الحالية
            var valueText = new TextBlock
            {
                Text = FormatStatValue(stat.CurrentValue, stat.StatName),
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Margin = new Thickness(0, 0, 0, 5)
            };

            // التغيير
            var changePanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            var trendIcon = stat.Trend == "up" ? "📈" : stat.Trend == "down" ? "📉" : "➡️";
            var trendColor = stat.Trend == "up" ? Color.FromRgb(40, 167, 69) : 
                           stat.Trend == "down" ? Color.FromRgb(220, 53, 69) : 
                           Color.FromRgb(108, 117, 125);

            var trendText = new TextBlock
            {
                Text = $"{trendIcon} {stat.ChangePercentage:F1}%",
                FontSize = 12,
                Foreground = new SolidColorBrush(trendColor),
                FontWeight = FontWeights.SemiBold
            };

            changePanel.Children.Add(trendText);

            stackPanel.Children.Add(titlePanel);
            stackPanel.Children.Add(valueText);
            stackPanel.Children.Add(changePanel);

            card.Child = stackPanel;
            return card;
        }

        // الحصول على اسم الإحصائية للعرض
        private string GetStatDisplayName(string statName)
        {
            return statName switch
            {
                "daily_sales" => "مبيعات اليوم",
                "weekly_sales" => "مبيعات الأسبوع",
                "monthly_sales" => "مبيعات الشهر",
                "daily_invoices" => "فواتير اليوم",
                "total_value" => "قيمة المخزون",
                "total_items" => "عدد الأصناف",
                "low_stock_items" => "مخزون منخفض",
                "expired_items" => "منتهي الصلاحية",
                "total_devices" => "إجمالي الأجهزة",
                "available_devices" => "أجهزة متاحة",
                "sold_devices" => "أجهزة مباعة",
                "active_installations" => "تنصيبات نشطة",
                "total_customers" => "إجمالي العملاء",
                "new_customers_month" => "عملاء جدد",
                "active_customers" => "عملاء نشطين",
                "monthly_revenue" => "الإيرادات الشهرية",
                "monthly_profit" => "الأرباح الشهرية",
                "pending_payments" => "مدفوعات معلقة",
                _ => statName
            };
        }

        // تنسيق قيمة الإحصائية
        private string FormatStatValue(decimal value, string statName)
        {
            if (statName.Contains("sales") || statName.Contains("revenue") || statName.Contains("profit") || 
                statName.Contains("value") || statName.Contains("payments"))
            {
                return $"{value:N0} د.ع";
            }
            else
            {
                return $"{value:N0}";
            }
        }

        // تحميل التنبيهات
        private async Task LoadAlertsAsync()
        {
            try
            {
                // إنشاء التنبيهات التلقائية
                await _alertService.GenerateAllAlertsAsync();

                // تحميل التنبيهات النشطة
                _currentAlerts = await _alertService.GetActiveAlertsAsync();
                
                // تطبيق الفلاتر
                ApplyAlertFilters();

                // تحديث الإحصائيات
                UpdateAlertStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تطبيق فلاتر التنبيهات
        private void ApplyAlertFilters()
        {
            var filteredAlerts = _currentAlerts.AsEnumerable();

            // فلترة حسب النوع
            if (AlertTypeFilterComboBox.SelectedIndex > 0)
            {
                var selectedType = ((ComboBoxItem)AlertTypeFilterComboBox.SelectedItem).Content.ToString();
                filteredAlerts = filteredAlerts.Where(a => a.AlertType == selectedType);
            }

            // فلترة حسب الأولوية
            if (AlertPriorityFilterComboBox.SelectedIndex > 0)
            {
                var selectedPriority = ((ComboBoxItem)AlertPriorityFilterComboBox.SelectedItem).Content.ToString();
                filteredAlerts = filteredAlerts.Where(a => a.Priority == selectedPriority);
            }

            AlertsDataGrid.ItemsSource = filteredAlerts.ToList();
        }

        // تحديث إحصائيات التنبيهات
        private void UpdateAlertStatistics()
        {
            var totalAlerts = _currentAlerts.Count;
            var highPriorityAlerts = _currentAlerts.Count(a => a.Priority == "عالي");
            var overdueAlerts = _currentAlerts.Count(a => a.DueDate.HasValue && a.DueDate < DateTime.Now);

            AlertsCountTextBlock.Text = $"إجمالي التنبيهات: {totalAlerts}";
            HighPriorityCountTextBlock.Text = $"عالية الأولوية: {highPriorityAlerts}";
            OverdueCountTextBlock.Text = $"متأخرة: {overdueAlerts}";
        }

        // معالجات الأحداث للبحث الذكي
        private void SmartSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SmartSearchTextBox.Text == "🔍 البحث الذكي عبر جميع الوحدات...")
            {
                SmartSearchTextBox.Text = "";
                SmartSearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
            }
        }

        private void SmartSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SmartSearchTextBox.Text))
            {
                SmartSearchTextBox.Text = "🔍 البحث الذكي عبر جميع الوحدات...";
                SmartSearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125));
            }
        }

        private void SmartSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // يمكن إضافة البحث التلقائي هنا
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformSearchAsync();
        }

        private async void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformAdvancedSearchAsync();
        }

        // تنفيذ البحث
        private async Task PerformSearchAsync()
        {
            try
            {
                var query = SmartSearchTextBox.Text;
                if (string.IsNullOrWhiteSpace(query) || query == "🔍 البحث الذكي عبر جميع الوحدات...")
                    return;

                _currentSearchResults = await _searchService.SmartSearchAsync(query);
                SearchResultsDataGrid.ItemsSource = _currentSearchResults;
                SearchResultsCountTextBlock.Text = $"تم العثور على {_currentSearchResults.Count} نتيجة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تنفيذ البحث المتقدم
        private async Task PerformAdvancedSearchAsync()
        {
            try
            {
                // يمكن تطوير البحث المتقدم هنا
                await PerformSearchAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث المتقدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // معالجات أحداث التنبيهات
        private async void GenerateAlertsButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadAlertsAsync();
            MessageBox.Show("تم إنشاء التنبيهات بنجاح!", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AlertTypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_currentAlerts != null)
                ApplyAlertFilters();
        }

        private void AlertPriorityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_currentAlerts != null)
                ApplyAlertFilters();
        }

        private async void MarkAllReadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                foreach (var alert in _currentAlerts.Where(a => a.Status == "نشط"))
                {
                    await _alertService.UpdateAlertStatusAsync(alert.Id, "مقروء");
                }
                await LoadAlertsAsync();
                MessageBox.Show("تم تحديد جميع التنبيهات كمقروءة!", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التنبيهات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void MarkReadButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var alert = button?.DataContext as SmartAlert;
            if (alert != null)
            {
                await _alertService.UpdateAlertStatusAsync(alert.Id, "مقروء");
                await LoadAlertsAsync();
            }
        }

        private async void SnoozeButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var alert = button?.DataContext as SmartAlert;
            if (alert != null)
            {
                await _alertService.UpdateAlertStatusAsync(alert.Id, "مؤجل");
                await LoadAlertsAsync();
            }
        }

        private async void CompleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var alert = button?.DataContext as SmartAlert;
            if (alert != null)
            {
                await _alertService.UpdateAlertStatusAsync(alert.Id, "مكتمل");
                await LoadAlertsAsync();
            }
        }

        // معالجات أحداث أخرى
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void ViewSearchResultButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var result = button?.DataContext as SearchResult;
            if (result != null)
            {
                // يمكن إضافة منطق فتح العنصر هنا
                MessageBox.Show($"عرض {result.Title}", "عرض العنصر", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void RebuildIndexButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await _searchService.BuildSearchIndexAsync();
                MessageBox.Show("تم إعادة بناء فهرس البحث بنجاح!", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة بناء الفهرس: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RecalculateStatsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadStatisticsAsync();
                MessageBox.Show("تم إعادة حساب الإحصائيات بنجاح!", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة حساب الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
