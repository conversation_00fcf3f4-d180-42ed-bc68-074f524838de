using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditDeviceWindow : Window
    {
        private MedicalDevice _device;
        private bool _isEditMode;
        private ObservableCollection<DeviceSerialNumber> _serialNumbers;
        
        public AddEditDeviceWindow(MedicalDevice device = null)
        {
            InitializeComponent();
            _device = device;
            _isEditMode = device != null;

            // إعداد مجموعة الأرقام التسلسلية
            _serialNumbers = new ObservableCollection<DeviceSerialNumber>();
            SerialNumbersDataGrid.ItemsSource = _serialNumbers;

            LoadSuppliersAsync();
            LoadCategoriesAsync();

            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل جهاز طبي";
                LoadDeviceData();
                _ = LoadSerialNumbersAsync(); // تشغيل بدون انتظار
            }
            else
            {
                // تعيين القيم الافتراضية
                PurchaseDatePicker.SelectedDate = DateTime.Now;
                WarrantyStartDatePicker.SelectedDate = DateTime.Now;
                WarrantyEndDatePicker.SelectedDate = DateTime.Now.AddYears(1);
                LocationComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
            }

            UpdateSerialCount();

            // إضافة معالج إغلاق النافذة للحفظ التلقائي
            this.Closing += AddEditDeviceWindow_Closing;
        }

        private async void AddEditDeviceWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // حفظ أي تغييرات معلقة فقط إذا كان Context متاحاً
                if (App.DatabaseContext != null && !App.DatabaseContext.IsDisposed)
                {
                    await App.DatabaseContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحفظ التلقائي عند إغلاق النافذة: {ex.Message}");
            }
        }
        
        private async void LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await App.DatabaseContext.Suppliers
                    .Where(s => s.Status == "نشط")
                    .Select(s => s.Name)
                    .ToListAsync();
                
                foreach (var supplier in suppliers)
                {
                    SupplierComboBox.Items.Add(supplier);
                }
                
                if (SupplierComboBox.Items.Count > 0)
                    SupplierComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.DeviceCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                CategoryComboBox.Items.Clear();

                foreach (var category in categories)
                {
                    CategoryComboBox.Items.Add($"{category.Icon} {category.Name}");
                }

                // إضافة فئات افتراضية إذا لم توجد فئات
                if (!categories.Any())
                {
                    CategoryComboBox.Items.Add("🏥 أجهزة الأشعة");
                    CategoryComboBox.Items.Add("🔬 أجهزة المختبر");
                    CategoryComboBox.Items.Add("💉 أجهزة الحقن");
                    CategoryComboBox.Items.Add("🩺 أجهزة الفحص");
                    CategoryComboBox.Items.Add("❤️ أجهزة القلب");
                    CategoryComboBox.Items.Add("🧠 أجهزة الأعصاب");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadDeviceData()
        {
            if (_device == null) return;
            
            NameTextBox.Text = _device.Name;
            BrandTextBox.Text = _device.Brand;
            ModelTextBox.Text = _device.Model;
            DescriptionTextBox.Text = _device.Description;
            PurchasePriceTextBox.Text = _device.PurchasePrice.ToString();
            SellingPriceTextBox.Text = _device.SellingPrice.ToString();
            PurchaseDatePicker.SelectedDate = _device.PurchaseDate;
            WarrantyStartDatePicker.SelectedDate = _device.WarrantyStartDate;
            WarrantyEndDatePicker.SelectedDate = _device.WarrantyEndDate;
            
            // تعيين الفئة
            foreach (var item in CategoryComboBox.Items)
            {
                if (item.ToString().Contains(_device.Category))
                {
                    CategoryComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين المورد
            foreach (var item in SupplierComboBox.Items)
            {
                if (item.ToString() == _device.Supplier)
                {
                    SupplierComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الموقع
            foreach (var item in LocationComboBox.Items)
            {
                if (item.ToString().Contains(_device.Location))
                {
                    LocationComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (var item in StatusComboBox.Items)
            {
                if (item.ToString().Contains(_device.Status))
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحميل المستندات
            UserManualPathTextBox.Text = _device.UserManualPath ?? "";
            MaintenanceManualPathTextBox.Text = _device.MaintenanceManualPath ?? "";
            OriginCertificatePathTextBox.Text = _device.OriginCertificatePath ?? "";
            QualityCertificatePathTextBox.Text = _device.QualityCertificatePath ?? "";
            OfficialCertificationsPathTextBox.Text = _device.OfficialCertificationsPath ?? "";
            TechnicalInfoBookletPathTextBox.Text = _device.TechnicalInfoBookletPath ?? "";
            ImportPapersPathTextBox.Text = _device.ImportPapersPath ?? "";
            ContractPapersPathTextBox.Text = _device.ContractPapersPath ?? "";

            // تحميل الأرقام التسلسلية للأجهزة الموجودة
            await LoadSerialNumbersAsync();
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            // التحقق من وجود أرقام تسلسلية
            if (_serialNumbers == null || _serialNumbers.Count == 0)
            {
                MessageBox.Show("يرجى إضافة رقم تسلسلي واحد على الأقل", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                if (_isEditMode)
                {
                    // في حالة التعديل، نحدث الجهاز الحالي فقط
                    UpdateDeviceData();
                    App.DatabaseContext.MedicalDevices.Update(_device);
                    _device.LastUpdated = DateTime.Now;

                    await App.DatabaseContext.SaveChangesAsync();
                    await SaveSerialNumbersAsync();

                    MessageBox.Show("تم تحديث الجهاز بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // في حالة الإضافة، ننشئ جهاز منفصل لكل رقم تسلسلي
                    await SaveMultipleDevicesAsync();

                    MessageBox.Show($"تم إضافة {_serialNumbers.Count} جهاز بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة البيانات: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task SaveMultipleDevicesAsync()
        {
            try
            {
                var savedDevices = new List<MedicalDevice>();

                // إنشاء جهاز منفصل لكل رقم تسلسلي
                foreach (var serialInfo in _serialNumbers)
                {
                    var newDevice = new MedicalDevice
                    {
                        Name = NameTextBox.Text.Trim(),
                        Brand = BrandTextBox.Text.Trim(),
                        Model = ModelTextBox.Text.Trim(),
                        SerialNumber = serialInfo.SerialNumber, // استخدام الرقم التسلسلي المحدد
                        Category = CategoryComboBox.Text,
                        Description = DescriptionTextBox.Text.Trim(),
                        PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text),
                        SellingPrice = decimal.Parse(SellingPriceTextBox.Text),
                        PurchaseDate = PurchaseDatePicker.SelectedDate ?? DateTime.Now,
                        WarrantyStartDate = WarrantyStartDatePicker.SelectedDate ?? DateTime.Now,
                        WarrantyEndDate = WarrantyEndDatePicker.SelectedDate ?? DateTime.Now.AddYears(1),
                        Supplier = SupplierComboBox.Text,
                        Location = LocationComboBox.Text,
                        Status = StatusComboBox.Text,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now,

                        // حفظ مسارات المستندات
                        UserManualPath = UserManualPathTextBox.Text.Trim(),
                        MaintenanceManualPath = MaintenanceManualPathTextBox.Text.Trim(),
                        OriginCertificatePath = OriginCertificatePathTextBox.Text.Trim(),
                        QualityCertificatePath = QualityCertificatePathTextBox.Text.Trim(),
                        OfficialCertificationsPath = OfficialCertificationsPathTextBox.Text.Trim(),
                        TechnicalInfoBookletPath = TechnicalInfoBookletPathTextBox.Text.Trim(),
                        ImportPapersPath = ImportPapersPathTextBox.Text.Trim(),
                        ContractPapersPath = ContractPapersPathTextBox.Text.Trim()
                    };

                    App.DatabaseContext.MedicalDevices.Add(newDevice);
                    savedDevices.Add(newDevice);

                    // إضافة الجهاز إلى المخزون تلقائياً
                    var inventoryItem = new InventoryItem
                    {
                        Name = $"{newDevice.Name} - {serialInfo.SerialNumber}",
                        Category = newDevice.Category,
                        Description = $"{newDevice.Description} - {serialInfo.ComponentName}",
                        CurrentStock = 1,
                        MinimumStock = 1,
                        Unit = "جهاز",
                        UnitPrice = newDevice.PurchasePrice,
                        Location = newDevice.Location,
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    App.DatabaseContext.InventoryItems.Add(inventoryItem);
                }

                // حفظ جميع الأجهزة
                await App.DatabaseContext.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine($"تم حفظ {savedDevices.Count} جهاز بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الأجهزة المتعددة: {ex.Message}");
                throw;
            }
        }

        private void UpdateDeviceData()
        {
            _device.Name = NameTextBox.Text.Trim();
            _device.Brand = BrandTextBox.Text.Trim();
            _device.Model = ModelTextBox.Text.Trim();
            _device.SerialNumber = ""; // سيتم حفظ الأرقام التسلسلية منفصلة
            _device.Category = CategoryComboBox.Text;
            _device.Description = DescriptionTextBox.Text.Trim();
            _device.PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
            _device.SellingPrice = decimal.Parse(SellingPriceTextBox.Text);
            _device.PurchaseDate = PurchaseDatePicker.SelectedDate ?? DateTime.Now;
            _device.WarrantyStartDate = WarrantyStartDatePicker.SelectedDate ?? DateTime.Now;
            _device.WarrantyEndDate = WarrantyEndDatePicker.SelectedDate ?? DateTime.Now.AddYears(1);
            _device.Supplier = SupplierComboBox.Text;
            _device.Location = LocationComboBox.Text;
            _device.Status = StatusComboBox.Text;

            // حفظ مسارات المستندات
            _device.UserManualPath = UserManualPathTextBox.Text.Trim();
            _device.MaintenanceManualPath = MaintenanceManualPathTextBox.Text.Trim();
            _device.OriginCertificatePath = OriginCertificatePathTextBox.Text.Trim();
            _device.QualityCertificatePath = QualityCertificatePathTextBox.Text.Trim();
            _device.OfficialCertificationsPath = OfficialCertificationsPathTextBox.Text.Trim();
            _device.TechnicalInfoBookletPath = TechnicalInfoBookletPathTextBox.Text.Trim();
            _device.ImportPapersPath = ImportPapersPathTextBox.Text.Trim();
            _device.ContractPapersPath = ContractPapersPathTextBox.Text.Trim();
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(BrandTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الماركة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                BrandTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(ModelTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الموديل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ModelTextBox.Focus();
                return false;
            }
            
            if (_serialNumbers.Count == 0)
            {
                MessageBox.Show("يرجى إضافة رقم تسلسلي واحد على الأقل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewSerialNumberTextBox.Focus();
                return false;
            }
            
            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(SellingPriceTextBox.Text, out decimal sellingPrice) || sellingPrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SellingPriceTextBox.Focus();
                return false;
            }
            
            if (PurchaseDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الشراء", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchaseDatePicker.Focus();
                return false;
            }
            
            if (WarrantyStartDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ بداية الضمان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyStartDatePicker.Focus();
                return false;
            }
            
            if (WarrantyEndDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ نهاية الضمان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyEndDatePicker.Focus();
                return false;
            }
            
            if (SupplierComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SupplierComboBox.Focus();
                return false;
            }
            
            if (LocationComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموقع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                LocationComboBox.Focus();
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الحالة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // دوال إدارة الفئات والملفات
        private void ManageCategoriesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageCategoriesWindow = new ManageCategoriesWindow();
            if (manageCategoriesWindow.ShowDialog() == true)
            {
                LoadCategoriesAsync();
            }
        }

        // دوال تصفح الملفات
        private void BrowseUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(UserManualPathTextBox, "اختيار كتيب المستخدم");
        }

        private void BrowseMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(MaintenanceManualPathTextBox, "اختيار كتيب الصيانة");
        }

        private void BrowseOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(OriginCertificatePathTextBox, "اختيار شهادة المنشأ");
        }

        private void BrowseQualityCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(QualityCertificatePathTextBox, "اختيار أوراق شهادة الجودة");
        }

        private void BrowseOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(OfficialCertificationsPathTextBox, "اختيار المصادقات الرسمية");
        }

        private void BrowseTechnicalInfoBookletBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(TechnicalInfoBookletPathTextBox, "اختيار كتيب المعلومات التقنية");
        }

        private void BrowseImportPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(ImportPapersPathTextBox, "اختيار أوراق الاستيراد");
        }

        private void BrowseContractPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(ContractPapersPathTextBox, "اختيار أوراق العقود");
        }

        // دوال مسح الملفات
        private void ClearUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            UserManualPathTextBox.Text = "";
        }

        private void ClearMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            MaintenanceManualPathTextBox.Text = "";
        }

        private void ClearOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            OriginCertificatePathTextBox.Text = "";
        }

        private void ClearQualityCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            QualityCertificatePathTextBox.Text = "";
        }

        private void ClearOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            OfficialCertificationsPathTextBox.Text = "";
        }

        private void ClearTechnicalInfoBookletBtn_Click(object sender, RoutedEventArgs e)
        {
            TechnicalInfoBookletPathTextBox.Text = "";
        }

        private void ClearImportPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            ImportPapersPathTextBox.Text = "";
        }

        private void ClearContractPapersBtn_Click(object sender, RoutedEventArgs e)
        {
            ContractPapersPathTextBox.Text = "";
        }

        // دالة مساعدة لتصفح الملفات
        private void BrowseFile(TextBox textBox, string title)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = title,
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.doc;*.docx)|*.doc;*.docx|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                textBox.Text = openFileDialog.FileName;
            }
        }

        // دوال إدارة الأرقام التسلسلية
        private async Task LoadSerialNumbersAsync()
        {
            if (_device?.Id > 0)
            {
                try
                {
                    var serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                        .Where(s => s.DeviceId == _device.Id && s.IsActive)
                        .OrderBy(s => s.SerialNumber)
                        .ToListAsync();

                    _serialNumbers.Clear();
                    foreach (var serial in serialNumbers)
                    {
                        _serialNumbers.Add(serial);
                    }
                    UpdateSerialCount();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AddSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(NewSerialNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم التسلسلي", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewSerialNumberTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(NewComponentNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكون", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewComponentNameTextBox.Focus();
                return;
            }

            // التحقق من عدم تكرار الرقم التسلسلي
            var existingSerial = _serialNumbers.FirstOrDefault(s =>
                s.SerialNumber.Equals(NewSerialNumberTextBox.Text.Trim(), StringComparison.OrdinalIgnoreCase));

            if (existingSerial != null)
            {
                MessageBox.Show("هذا الرقم التسلسلي موجود بالفعل!", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewSerialNumberTextBox.Focus();
                return;
            }

            // إضافة رقم تسلسلي جديد
            var newSerial = new DeviceSerialNumber
            {
                SerialNumber = NewSerialNumberTextBox.Text.Trim(),
                ComponentName = NewComponentNameTextBox.Text.Trim(),
                ComponentType = "مكون أساسي",
                Status = "نشط",
                IsActive = true,
                CreatedDate = DateTime.Now,
                LastUpdated = DateTime.Now
            };

            _serialNumbers.Add(newSerial);
            UpdateSerialCount();

            // مسح النموذج
            NewSerialNumberTextBox.Clear();
            NewComponentNameTextBox.Clear();
            NewSerialNumberTextBox.Focus();
        }

        private void RemoveSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is DeviceSerialNumber serialNumber)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف الرقم التسلسلي '{serialNumber.SerialNumber}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );

                if (result == MessageBoxResult.Yes)
                {
                    _serialNumbers.Remove(serialNumber);
                    UpdateSerialCount();
                }
            }
        }

        private void UpdateSerialCount()
        {
            SerialCountTextBlock.Text = $"العدد: {_serialNumbers.Count}";
        }

        private async Task SaveSerialNumbersAsync()
        {
            if (_device?.Id > 0)
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"حفظ الأرقام التسلسلية للجهاز ID: {_device.Id}");
                    System.Diagnostics.Debug.WriteLine($"عدد الأرقام التسلسلية المراد حفظها: {_serialNumbers?.Count ?? 0}");

                    // حذف الأرقام التسلسلية الموجودة (حذف منطقي)
                    var existingSerials = await App.DatabaseContext.DeviceSerialNumbers
                        .Where(s => s.DeviceId == _device.Id)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"عدد الأرقام التسلسلية الموجودة: {existingSerials.Count}");

                    foreach (var existing in existingSerials)
                    {
                        existing.IsActive = false;
                        existing.LastUpdated = DateTime.Now;
                    }

                    // إضافة الأرقام التسلسلية الجديدة
                    if (_serialNumbers != null)
                    {
                        foreach (var serial in _serialNumbers)
                        {
                            System.Diagnostics.Debug.WriteLine($"حفظ رقم تسلسلي: {serial.SerialNumber} - {serial.ComponentName}");

                            // إنشاء كائن جديد لتجنب مشاكل التتبع
                            var newSerial = new DeviceSerialNumber
                            {
                                DeviceId = _device.Id,
                                SerialNumber = serial.SerialNumber,
                                ComponentName = serial.ComponentName,
                                ComponentType = serial.ComponentType ?? "مكون أساسي",
                                Status = serial.Status ?? "نشط",
                                IsActive = true,
                                CreatedDate = DateTime.Now,
                                LastUpdated = DateTime.Now
                            };

                            App.DatabaseContext.DeviceSerialNumbers.Add(newSerial);
                        }
                    }

                    var saveResult = await App.DatabaseContext.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"نتيجة حفظ الأرقام التسلسلية: {saveResult} صفوف متأثرة");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الأرقام التسلسلية: {ex.Message}");
                    MessageBox.Show($"خطأ في حفظ الأرقام التسلسلية: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("لا يمكن حفظ الأرقام التسلسلية - معرف الجهاز غير صالح");
            }
        }
    }
}
