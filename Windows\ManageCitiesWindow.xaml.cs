using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageCitiesWindow : Window
    {
        private City _editingCity = null;
        private List<City> _allCities;
        
        public ManageCitiesWindow()
        {
            InitializeComponent();
            LoadCitiesAsync();
            LoadCountryFilter();
        }
        
        private async void LoadCitiesAsync()
        {
            try
            {
                _allCities = await App.DatabaseContext.Cities
                    .OrderBy(c => c.Country)
                    .ThenBy(c => c.Name)
                    .ToListAsync();
                    
                CitiesDataGrid.ItemsSource = _allCities;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدن: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadCountryFilter()
        {
            CountryFilterComboBox.Items.Clear();
            CountryFilterComboBox.Items.Add("جميع البلدان");
            
            var countries = new[]
            {
                "العراق", "السعودية", "الكويت", "قطر", "البحرين", "الإمارات", "عمان",
                "الأردن", "سوريا", "لبنان", "مصر", "ليبيا", "تونس", "الجزائر", "المغرب", "اليمن"
            };
            
            foreach (var country in countries)
            {
                CountryFilterComboBox.Items.Add(country);
            }
            
            CountryFilterComboBox.SelectedIndex = 0;
        }
        
        private void CountryFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allCities == null) return;
            
            var selectedCountry = CountryFilterComboBox.SelectedItem?.ToString();
            
            if (selectedCountry == "جميع البلدان")
            {
                CitiesDataGrid.ItemsSource = _allCities;
            }
            else
            {
                var filteredCities = _allCities.Where(c => c.Country == selectedCountry).ToList();
                CitiesDataGrid.ItemsSource = filteredCities;
            }
        }
        
        private void AddCityBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingCity = null;
            CityNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadCitiesAsync();
            ClearForm();
        }
        
        private void EditCityBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var city = button?.DataContext as City;
            
            if (city != null)
            {
                _editingCity = city;
                LoadCityToForm(city);
            }
        }
        
        private async void DeleteCityBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var city = button?.DataContext as City;
            
            if (city != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المدينة '{city.Name} - {city.Country}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        App.DatabaseContext.Cities.Remove(city);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف المدينة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadCitiesAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المدينة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveCityBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingCity == null)
                {
                    // إضافة مدينة جديدة
                    var newCity = new City
                    {
                        Name = CityNameTextBox.Text.Trim(),
                        Country = CountryComboBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.Cities.Add(newCity);
                }
                else
                {
                    // تعديل مدينة موجودة
                    _editingCity.Name = CityNameTextBox.Text.Trim();
                    _editingCity.Country = CountryComboBox.Text.Trim();
                    _editingCity.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingCity.LastUpdated = DateTime.Now;
                    
                    App.DatabaseContext.Cities.Update(_editingCity);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingCity == null ? "تم إضافة المدينة بنجاح!" : "تم تحديث المدينة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadCitiesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المدينة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadCityToForm(City city)
        {
            CityNameTextBox.Text = city.Name;
            CountryComboBox.Text = city.Country;
            IsActiveCheckBox.IsChecked = city.IsActive;
        }
        
        private void ClearForm()
        {
            _editingCity = null;
            CityNameTextBox.Text = "";
            CountryComboBox.SelectedIndex = 0;
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(CityNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المدينة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CityNameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(CountryComboBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم البلد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CountryComboBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
