using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Input;
using MedicalDevicesManager.Controls;

namespace MedicalDevicesManager.Examples
{
    /// <summary>
    /// مثال على استخدام الجدول المتقدم
    /// </summary>
    public partial class AdvancedDataGridExample : Window
    {
        private AdvancedDataGrid _advancedGrid;
        private List<MedicalDevice> _devices;

        public AdvancedDataGridExample()
        {
            InitializeComponent();
            LoadSampleData();
            SetupAdvancedGrid();
        }

        private void InitializeComponent()
        {
            Title = "مثال على الجدول المتقدم";
            Width = 1200;
            Height = 800;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // العنوان
            var title = new TextBlock
            {
                Text = "🏥 إدارة الأجهزة الطبية - الجدول المتقدم",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(20),
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };
            Grid.SetRow(title, 0);
            mainGrid.Children.Add(title);

            // الجدول المتقدم
            _advancedGrid = new AdvancedDataGrid
            {
                EnablePagination = true,
                PageSize = 25,
                EnableAdvancedSearch = true,
                EnableExport = true,
                EnablePrint = true,
                EnableColumnFreeze = true,
                EnableRowNumbers = true,
                EnableMultiSelect = true,
                EnableQuickPreview = true,
                EnableColumnCustomization = true,
                EnableAdvancedFiltering = true,
                Margin = new Thickness(20)
            };

            Grid.SetRow(_advancedGrid, 1);
            mainGrid.Children.Add(_advancedGrid);

            Content = mainGrid;
        }

        private void LoadSampleData()
        {
            _devices = new List<MedicalDevice>();

            // إنشاء بيانات تجريبية
            var categories = new[] { "أجهزة تشخيص", "أجهزة علاج", "أجهزة مراقبة", "أجهزة جراحة", "أجهزة طوارئ" };
            var statuses = new[] { "نشط", "غير نشط", "قيد الصيانة", "متاح", "محجوز" };
            var suppliers = new[] { "شركة الطب المتقدم", "مؤسسة الأجهزة الطبية", "شركة التكنولوجيا الصحية", "مجموعة الرعاية الطبية" };
            var locations = new[] { "المستودع الرئيسي", "قسم الطوارئ", "غرفة العمليات", "العيادة الخارجية", "قسم الأشعة" };

            var random = new Random();

            for (int i = 1; i <= 150; i++)
            {
                var device = new MedicalDevice
                {
                    Id = i,
                    Name = $"جهاز طبي {i}",
                    Brand = $"ماركة {random.Next(1, 10)}",
                    Model = $"موديل-{random.Next(100, 999)}",
                    SerialNumber = $"SN{random.Next(10000, 99999)}",
                    Category = categories[random.Next(categories.Length)],
                    Description = $"وصف الجهاز الطبي رقم {i}",
                    PurchasePrice = random.Next(5000, 50000),
                    SellingPrice = random.Next(6000, 60000),
                    PurchaseDate = DateTime.Now.AddDays(-random.Next(1, 1000)),
                    WarrantyStartDate = DateTime.Now.AddDays(-random.Next(1, 365)),
                    WarrantyEndDate = DateTime.Now.AddDays(random.Next(30, 730)),
                    Supplier = suppliers[random.Next(suppliers.Length)],
                    Location = locations[random.Next(locations.Length)],
                    Status = statuses[random.Next(statuses.Length)],
                    CreatedDate = DateTime.Now.AddDays(-random.Next(1, 100)),
                    LastUpdated = DateTime.Now.AddDays(-random.Next(1, 30))
                };

                _devices.Add(device);
            }
        }

        private void SetupAdvancedGrid()
        {
            // إعداد الأعمدة
            _advancedGrid.AddTextColumn("الاسم", "Name", 150);
            _advancedGrid.AddTextColumn("الماركة", "Brand", 100);
            _advancedGrid.AddTextColumn("الموديل", "Model", 100);
            _advancedGrid.AddTextColumn("الرقم التسلسلي", "SerialNumber", 120);
            _advancedGrid.AddTextColumn("الفئة", "Category", 120);
            _advancedGrid.AddCurrencyColumn("سعر الشراء", "PurchasePrice", 120);
            _advancedGrid.AddCurrencyColumn("سعر البيع", "SellingPrice", 120);
            _advancedGrid.AddDateColumn("تاريخ الشراء", "PurchaseDate", "yyyy/MM/dd", 120);
            _advancedGrid.AddDateColumn("بداية الضمان", "WarrantyStartDate", "yyyy/MM/dd", 120);
            _advancedGrid.AddDateColumn("نهاية الضمان", "WarrantyEndDate", "yyyy/MM/dd", 120);
            _advancedGrid.AddTextColumn("المورد", "Supplier", 150);
            _advancedGrid.AddTextColumn("الموقع", "Location", 120);
            _advancedGrid.AddTextColumn("الحالة", "Status", 100);

            // إضافة عمود الإجراءات
            var actions = new List<ActionButton>
            {
                new ActionButton
                {
                    Icon = "✏️",
                    Tooltip = "تعديل",
                    BackgroundColor = Color.FromRgb(0, 123, 255),
                    ClickHandler = EditDevice_Click
                },
                new ActionButton
                {
                    Icon = "👁️",
                    Tooltip = "عرض",
                    BackgroundColor = Color.FromRgb(40, 167, 69),
                    ClickHandler = ViewDevice_Click
                },
                new ActionButton
                {
                    Icon = "🗑️",
                    Tooltip = "حذف",
                    BackgroundColor = Color.FromRgb(220, 53, 69),
                    ClickHandler = DeleteDevice_Click
                }
            };

            _advancedGrid.AddActionsColumn("الإجراءات", actions, 120);

            // إعداد الأحداث
            _advancedGrid.RowDoubleClick += AdvancedGrid_RowDoubleClick;
            _advancedGrid.RowSelected += AdvancedGrid_RowSelected;
            _advancedGrid.MultipleRowsSelected += AdvancedGrid_MultipleRowsSelected;
            _advancedGrid.DataRefreshRequested += AdvancedGrid_DataRefreshRequested;

            // تعيين البيانات
            _advancedGrid.SetDataSource(_devices);
        }

        #region Event Handlers

        private void AdvancedGrid_RowDoubleClick(object sender, object selectedItem)
        {
            if (selectedItem is MedicalDevice device)
            {
                MessageBox.Show($"تم النقر المزدوج على الجهاز: {device.Name}", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void AdvancedGrid_RowSelected(object sender, object selectedItem)
        {
            if (selectedItem is MedicalDevice device)
            {
                // يمكن عرض تفاصيل الجهاز في لوحة جانبية
                ShowDeviceDetails(device);
            }
        }

        private void AdvancedGrid_MultipleRowsSelected(object sender, List<object> selectedItems)
        {
            var devices = selectedItems.Cast<MedicalDevice>().ToList();
            MessageBox.Show($"تم تحديد {devices.Count} جهاز", "تحديد متعدد", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AdvancedGrid_DataRefreshRequested(object sender, EventArgs e)
        {
            // إعادة تحميل البيانات من قاعدة البيانات
            RefreshData();
        }

        private void EditDevice_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalDevice device)
            {
                MessageBox.Show($"تعديل الجهاز: {device.Name}", "تعديل", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                // فتح نافذة التعديل
            }
        }

        private void ViewDevice_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalDevice device)
            {
                ShowDeviceDetails(device);
            }
        }

        private void DeleteDevice_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is MedicalDevice device)
            {
                var result = MessageBox.Show($"هل تريد حذف الجهاز: {device.Name}؟", "تأكيد الحذف", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _devices.Remove(device);
                    _advancedGrid.SetDataSource(_devices);
                    MessageBox.Show("تم حذف الجهاز بنجاح", "نجح الحذف", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        #endregion

        #region Helper Methods

        private void ShowDeviceDetails(MedicalDevice device)
        {
            var details = $"تفاصيل الجهاز:\n\n" +
                         $"الاسم: {device.Name}\n" +
                         $"الماركة: {device.Brand}\n" +
                         $"الموديل: {device.Model}\n" +
                         $"الرقم التسلسلي: {device.SerialNumber}\n" +
                         $"الفئة: {device.Category}\n" +
                         $"سعر الشراء: {device.PurchasePrice:C}\n" +
                         $"سعر البيع: {device.SellingPrice:C}\n" +
                         $"تاريخ الشراء: {device.PurchaseDate:yyyy/MM/dd}\n" +
                         $"المورد: {device.Supplier}\n" +
                         $"الموقع: {device.Location}\n" +
                         $"الحالة: {device.Status}";

            MessageBox.Show(details, "تفاصيل الجهاز", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshData()
        {
            // محاكاة إعادة تحميل البيانات
            LoadSampleData();
            _advancedGrid.SetDataSource(_devices);
            MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Menu Actions

        /// <summary>
        /// إضافة شريط قوائم للنافذة
        /// </summary>
        private void AddMenuBar()
        {
            var menu = new Menu();
            
            // قائمة الملف
            var fileMenu = new MenuItem { Header = "ملف" };
            fileMenu.Items.Add(new MenuItem { Header = "تصدير إلى Excel", Command = new RelayCommand(() => _advancedGrid.QuickExportToExcel()) });
            fileMenu.Items.Add(new MenuItem { Header = "طباعة", Command = new RelayCommand(() => _advancedGrid.QuickPrint()) });
            fileMenu.Items.Add(new Separator());
            fileMenu.Items.Add(new MenuItem { Header = "إغلاق", Command = new RelayCommand(() => Close()) });
            
            // قائمة التحرير
            var editMenu = new MenuItem { Header = "تحرير" };
            editMenu.Items.Add(new MenuItem { Header = "نسخ المحدد", Command = new RelayCommand(() => _advancedGrid.CopySelectedToClipboard()) });
            editMenu.Items.Add(new MenuItem { Header = "تحديد الكل", Command = new RelayCommand(() => SelectAll()) });
            
            // قائمة العرض
            var viewMenu = new MenuItem { Header = "عرض" };
            viewMenu.Items.Add(new MenuItem { Header = "تحديث", Command = new RelayCommand(() => RefreshData()) });
            viewMenu.Items.Add(new MenuItem { Header = "إحصائيات", Command = new RelayCommand(() => ShowStatistics()) });

            menu.Items.Add(fileMenu);
            menu.Items.Add(editMenu);
            menu.Items.Add(viewMenu);

            // إضافة القائمة للنافذة
            DockPanel.SetDock(menu, Dock.Top);
            var dockPanel = new DockPanel();
            dockPanel.Children.Add(menu);
            dockPanel.Children.Add((UIElement)Content);
            Content = dockPanel;
        }

        private void SelectAll()
        {
            // تحديد جميع العناصر
            // يحتاج تنفيذ في الجدول المتقدم
        }

        private void ShowStatistics()
        {
            var stats = _advancedGrid.GetDataStatistics();
            var message = $"إحصائيات البيانات:\n\n" +
                         $"إجمالي السجلات: {stats.TotalRecords}\n" +
                         $"السجلات المفلترة: {stats.FilteredRecords}\n" +
                         $"السجلات المحددة: {stats.SelectedRecords}\n" +
                         $"سجلات الصفحة الحالية: {stats.CurrentPageRecords}";

            MessageBox.Show(message, "إحصائيات البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion
    }

    /// <summary>
    /// أمر بسيط للقوائم
    /// </summary>
    public class RelayCommand : System.Windows.Input.ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object parameter)
        {
            _execute();
        }
    }
}
