using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageCategoriesWindow : Window
    {
        private DeviceCategory _editingCategory = null;
        
        public ManageCategoriesWindow()
        {
            InitializeComponent();
            LoadCategoriesAsync();
        }
        
        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.DeviceCategories
                    .OrderBy(c => c.Name)
                    .ToListAsync();
                    
                CategoriesDataGrid.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingCategory = null;
            CategoryNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadCategoriesAsync();
            ClearForm();
        }
        
        private void EditCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var category = button?.DataContext as DeviceCategory;
            
            if (category != null)
            {
                _editingCategory = category;
                LoadCategoryToForm(category);
            }
        }
        
        private async void DeleteCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var category = button?.DataContext as DeviceCategory;
            
            if (category != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفئة '{category.Name}'؟\n\nسيتم تحويل جميع الأجهزة في هذه الفئة إلى 'غير محدد'.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // تحديث الأجهزة التي تستخدم هذه الفئة
                        var devicesWithCategory = await App.DatabaseContext.MedicalDevices
                            .Where(d => d.Category == category.Name)
                            .ToListAsync();
                            
                        foreach (var device in devicesWithCategory)
                        {
                            device.Category = "غير محدد";
                        }
                        
                        App.DatabaseContext.DeviceCategories.Remove(category);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الفئة بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadCategoriesAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveCategoryBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                var iconText = IconComboBox.Text.Split(' ')[0]; // استخراج الأيقونة فقط
                
                if (_editingCategory == null)
                {
                    // إضافة فئة جديدة
                    var newCategory = new DeviceCategory
                    {
                        Name = CategoryNameTextBox.Text.Trim(),
                        Description = CategoryDescriptionTextBox.Text.Trim(),
                        Icon = iconText,
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.DeviceCategories.Add(newCategory);
                }
                else
                {
                    // تعديل فئة موجودة
                    var oldName = _editingCategory.Name;
                    
                    _editingCategory.Name = CategoryNameTextBox.Text.Trim();
                    _editingCategory.Description = CategoryDescriptionTextBox.Text.Trim();
                    _editingCategory.Icon = iconText;
                    _editingCategory.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingCategory.LastUpdated = DateTime.Now;
                    
                    // تحديث الأجهزة التي تستخدم هذه الفئة
                    if (oldName != _editingCategory.Name)
                    {
                        var devicesWithCategory = await App.DatabaseContext.MedicalDevices
                            .Where(d => d.Category == oldName)
                            .ToListAsync();
                            
                        foreach (var device in devicesWithCategory)
                        {
                            device.Category = _editingCategory.Name;
                        }
                    }
                    
                    App.DatabaseContext.DeviceCategories.Update(_editingCategory);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingCategory == null ? "تم إضافة الفئة بنجاح!" : "تم تحديث الفئة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadCategoriesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadCategoryToForm(DeviceCategory category)
        {
            CategoryNameTextBox.Text = category.Name;
            CategoryDescriptionTextBox.Text = category.Description;
            IsActiveCheckBox.IsChecked = category.IsActive;
            
            // البحث عن الأيقونة في القائمة
            foreach (ComboBoxItem item in IconComboBox.Items)
            {
                if (item.Content.ToString().StartsWith(category.Icon))
                {
                    IconComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private void ClearForm()
        {
            _editingCategory = null;
            CategoryNameTextBox.Text = "";
            CategoryDescriptionTextBox.Text = "";
            IconComboBox.SelectedIndex = 0;
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(CategoryNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryNameTextBox.Focus();
                return false;
            }
            
            if (IconComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار أيقونة للفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                IconComboBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
