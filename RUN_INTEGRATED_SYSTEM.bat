@echo off
echo ========================================
echo    Medical Devices Management System v6.0
echo    Integrated Edition - Full CRUD Operations
echo ========================================
echo.
echo New Features:
echo - Add, Edit, Delete Medical Devices
echo - Add, Edit, Delete Inventory Items
echo - Database Integration Between Modules
echo - Comprehensive Test Data for All Modules
echo - Advanced Interactive Interfaces
echo.
echo Building and starting the integrated system...
echo.

dotnet build --configuration Release
if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the system...
    echo.
    dotnet run --configuration Release
) else (
    echo.
    echo Build failed!
    echo Please check the errors above
)

echo.
echo System closed
pause
