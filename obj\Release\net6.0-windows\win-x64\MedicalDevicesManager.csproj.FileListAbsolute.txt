D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\MedicalDevicesManager.exe
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\MedicalDevicesManager.deps.json
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\MedicalDevicesManager.runtimeconfig.json
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\MedicalDevicesManager.pdb
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ClosedXML.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ClosedXML.Parser.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\DocumentFormat.OpenXml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\DocumentFormat.OpenXml.Framework.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\EPPlus.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\EPPlus.Interfaces.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\EPPlus.System.Drawing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ExcelNumberFormat.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.barcodes.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.bouncy-castle-connector.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.forms.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.io.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.kernel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.layout.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.pdfa.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.sign.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.styledxmlparser.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.svg.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\itext.commons.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Data.Sqlite.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.DotNet.PlatformAbstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.EntityFrameworkCore.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.EntityFrameworkCore.Relational.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Caching.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Caching.Memory.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Configuration.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Configuration.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Configuration.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.DependencyInjection.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.DependencyModel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.FileProviders.Physical.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.FileSystemGlobbing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Logging.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Logging.Abstractions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Options.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Extensions.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.IO.RecyclableMemoryStream.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Newtonsoft.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\RBush.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\SixLabors.Fonts.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\SQLitePCLRaw.batteries_v2.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\SQLitePCLRaw.core.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\SQLitePCLRaw.provider.e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Packaging.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.Encodings.Web.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\e_sqlite3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.CSharp.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.VisualBasic.Core.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Win32.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Win32.Registry.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.AppContext.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Buffers.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Collections.Concurrent.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Collections.Immutable.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Collections.NonGeneric.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Collections.Specialized.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Collections.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.Annotations.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.DataAnnotations.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.EventBasedAsync.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.TypeConverter.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ComponentModel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Configuration.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Console.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Core.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Data.Common.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Data.DataSetExtensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Data.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.Contracts.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.Debug.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.DiagnosticSource.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.FileVersionInfo.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.Process.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.StackTrace.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.TextWriterTraceListener.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.Tools.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.TraceSource.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.Tracing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Drawing.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Dynamic.Runtime.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Formats.Asn1.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Globalization.Calendars.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Globalization.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Globalization.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Compression.Brotli.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Compression.FileSystem.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Compression.ZipFile.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Compression.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.FileSystem.AccessControl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.FileSystem.DriveInfo.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.FileSystem.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.FileSystem.Watcher.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.FileSystem.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.IsolatedStorage.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.MemoryMappedFiles.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Pipes.AccessControl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Pipes.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.UnmanagedMemoryStream.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Linq.Expressions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Linq.Parallel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Linq.Queryable.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Linq.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Memory.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Http.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Http.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.HttpListener.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Mail.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.NameResolution.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.NetworkInformation.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Ping.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Quic.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Requests.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Security.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.ServicePoint.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.Sockets.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.WebClient.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.WebHeaderCollection.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.WebProxy.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.WebSockets.Client.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.WebSockets.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Net.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Numerics.Vectors.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Numerics.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ObjectModel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Private.CoreLib.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Private.DataContractSerialization.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Private.Uri.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Private.Xml.Linq.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Private.Xml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.DispatchProxy.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Emit.ILGeneration.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Emit.Lightweight.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Emit.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Metadata.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.TypeExtensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Reflection.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Resources.Reader.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Resources.ResourceManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Resources.Writer.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.CompilerServices.Unsafe.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.CompilerServices.VisualC.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Handles.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.InteropServices.RuntimeInformation.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.InteropServices.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Intrinsics.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Loader.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Numerics.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Serialization.Formatters.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Serialization.Json.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Serialization.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Serialization.Xml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.Serialization.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Runtime.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.AccessControl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Claims.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Algorithms.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Cng.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Csp.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Encoding.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.OpenSsl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.X509Certificates.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Principal.Windows.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Principal.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.SecureString.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ServiceModel.Web.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ServiceProcess.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.Encoding.CodePages.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.Encoding.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.Encoding.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Text.RegularExpressions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Channels.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Overlapped.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Tasks.Dataflow.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Tasks.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Tasks.Parallel.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Tasks.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Thread.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.ThreadPool.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.Timer.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Transactions.Local.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Transactions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.ValueTuple.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Web.HttpUtility.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Web.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.Linq.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.ReaderWriter.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.Serialization.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.XDocument.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.XPath.XDocument.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.XPath.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.XmlDocument.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.XmlSerializer.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\mscorlib.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\netstandard.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Accessibility.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\DirectWriteForwarder.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.VisualBasic.Forms.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.VisualBasic.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Win32.Registry.AccessControl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.Win32.SystemEvents.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationCore.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework-SystemCore.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework-SystemData.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework-SystemDrawing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework-SystemXml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework-SystemXmlLinq.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.Aero.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.Aero2.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.AeroLite.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.Classic.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.Luna.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.Royale.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationFramework.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationUI.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ReachFramework.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.CodeDom.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Configuration.ConfigurationManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Design.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.EventLog.Messages.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.EventLog.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Diagnostics.PerformanceCounter.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.DirectoryServices.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Drawing.Common.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Drawing.Design.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Drawing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Printing.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Resources.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Pkcs.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.ProtectedData.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Cryptography.Xml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Security.Permissions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Threading.AccessControl.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Controls.Ribbon.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Extensions.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Forms.Design.Editors.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Forms.Design.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Forms.Primitives.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Forms.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Input.Manipulations.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Windows.Presentation.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.Xaml.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\UIAutomationClient.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\UIAutomationClientSideProviders.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\UIAutomationProvider.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\UIAutomationTypes.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\WindowsBase.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\WindowsFormsIntegration.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\Microsoft.DiaSymReader.Native.amd64.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\System.IO.Compression.Native.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-console-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-console-l1-2-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-datetime-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-debug-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-errorhandling-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-fibers-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-file-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-file-l1-2-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-file-l2-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-handle-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-heap-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-interlocked-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-libraryloader-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-localization-l1-2-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-memory-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-namedpipe-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-processenvironment-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-processthreads-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-processthreads-l1-1-1.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-profile-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-rtlsupport-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-string-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-synch-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-synch-l1-2-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-sysinfo-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-timezone-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-core-util-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-conio-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-convert-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-environment-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-filesystem-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-heap-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-locale-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-math-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-multibyte-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-private-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-process-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-runtime-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-stdio-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-string-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-time-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\api-ms-win-crt-utility-l1-1-0.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\clretwrc.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\clrjit.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\coreclr.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\createdump.exe
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\dbgshim.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\hostfxr.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\hostpolicy.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\mscordaccore.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\mscordaccore_amd64_amd64_6.0.3624.51421.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\mscordbi.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\mscorrc.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\msquic.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ucrtbase.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\D3DCompiler_47_cor3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PenImc_cor3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\PresentationNative_cor3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\vcruntime140_cor3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\wpfgfx_cor3.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\cs\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\de\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\es\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\fr\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\it\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ja\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ko\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pl\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\pt-BR\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\ru\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\tr\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hans\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\Microsoft.VisualBasic.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\PresentationCore.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\PresentationFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\PresentationUI.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\ReachFramework.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Windows.Controls.Ribbon.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Windows.Forms.Design.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Windows.Forms.Primitives.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Windows.Forms.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Windows.Input.Manipulations.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\System.Xaml.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\UIAutomationClient.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\UIAutomationClientSideProviders.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\UIAutomationProvider.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\UIAutomationTypes.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\WindowsBase.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\bin\Release\net6.0-windows\win-x64\zh-Hant\WindowsFormsIntegration.resources.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.csproj.AssemblyReference.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MainWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditCustomerWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditDeviceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditInstallationWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditInventoryWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditMaintenanceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSaleWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditShipmentWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSparePartWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSupplierWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AdvancedInventorySettingsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\BackupWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\CreateBackupWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\DeviceDetailsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ExportWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\InventoryItemReportWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\InventorySelectionWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCategoriesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCitiesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCustomerTypesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageInventoryCategoriesWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageRecipientsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageSerialNumbersWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageTechniciansWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\NotificationsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ReportsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SettingsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SmartSystemWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\App.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MainWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditCustomerWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditDeviceWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditInstallationWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditInventoryWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditMaintenanceWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSaleWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditShipmentWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSparePartWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AddEditSupplierWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\AdvancedInventorySettingsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\BackupWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\CreateBackupWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\DeviceDetailsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ExportWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\InventoryItemReportWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\InventorySelectionWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCategoriesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCitiesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageCustomerTypesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageInventoryCategoriesWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageRecipientsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageSerialNumbersWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ManageTechniciansWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\NotificationsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\ReportsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SettingsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SmartSystemWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\App.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager_MarkupCompile.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.g.resources
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.GeneratedMSBuildEditorConfig.editorconfig
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.AssemblyInfoInputs.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.AssemblyInfo.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.csproj.CoreCompileInputs.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalD.3D21AE2B.Up2Date
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\refint\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.pdb
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\MedicalDevicesManager.genruntimeconfig.cache
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\ref\MedicalDevicesManager.dll
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\DeviceSerialNumbersWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\MaintenanceDetailsWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SelectDeviceWindow.baml
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\DeviceSerialNumbersWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\MaintenanceDetailsWindow.g.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Release\net6.0-windows\win-x64\Windows\SelectDeviceWindow.g.cs
