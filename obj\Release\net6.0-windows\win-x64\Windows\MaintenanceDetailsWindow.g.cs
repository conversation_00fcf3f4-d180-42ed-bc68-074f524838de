﻿#pragma checksum "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1F586EC094EAF79743506EAD971ACC75F82C5287"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// MaintenanceDetailsWindow
    /// </summary>
    public partial class MaintenanceDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextMaintenanceDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicianNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierCompanyTextBlock;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CostTextBlock;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdatedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportBookletTextBlock;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewReportBtn;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewDocumentsBtn;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/maintenancedetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DeviceNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SerialNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MaintenanceTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.MaintenanceDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.NextMaintenanceDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TechnicianNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.SupplierCompanyTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CostTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LastUpdatedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.NotesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ReportBookletTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.ViewReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
            this.ViewReportBtn.Click += new System.Windows.RoutedEventHandler(this.ViewReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.DocumentsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ViewDocumentsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 139 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
            this.ViewDocumentsBtn.Click += new System.Windows.RoutedEventHandler(this.ViewDocumentsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 155 "..\..\..\..\..\Windows\MaintenanceDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

