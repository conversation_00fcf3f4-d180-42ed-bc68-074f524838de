using System;
using System.Windows;

namespace MedicalDevicesManager.Windows
{
    public partial class CreateBackupWindow : Window
    {
        public string BackupName { get; private set; }
        public string Description { get; private set; }
        public bool CompressBackup { get; private set; }
        public bool VerifyBackup { get; private set; }
        public bool DeleteOldBackups { get; private set; }
        
        public CreateBackupWindow()
        {
            InitializeComponent();
            
            // تعيين القيم الافتراضية
            BackupNameTextBox.Text = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
            DescriptionTextBox.Text = "نسخة احتياطية يدوية";
            
            BackupNameTextBox.Focus();
            BackupNameTextBox.SelectAll();
        }
        
        private void CreateBtn_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                BackupName = BackupNameTextBox.Text.Trim();
                Description = DescriptionTextBox.Text.Trim();
                CompressBackup = CompressBackupCheckBox.IsChecked ?? false;
                VerifyBackup = VerifyBackupCheckBox.IsChecked ?? false;
                DeleteOldBackups = DeleteOldBackupsCheckBox.IsChecked ?? false;
                
                DialogResult = true;
                Close();
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(BackupNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم النسخة الاحتياطية", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                BackupNameTextBox.Focus();
                return false;
            }
            
            // التحقق من الأحرف غير المسموحة في اسم الملف
            var invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in BackupNameTextBox.Text)
            {
                if (Array.IndexOf(invalidChars, c) >= 0)
                {
                    MessageBox.Show($"اسم النسخة الاحتياطية يحتوي على أحرف غير مسموحة: {c}", 
                        "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    BackupNameTextBox.Focus();
                    return false;
                }
            }
            
            return true;
        }
    }
}
