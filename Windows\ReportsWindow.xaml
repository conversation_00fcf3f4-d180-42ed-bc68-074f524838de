<Window x:Class="MedicalDevicesManager.Windows.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المتقدمة" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📊 التقارير المتقدمة والتفاعلية" 
                   FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- أزرار التقارير والإجراءات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- أزرار التقارير -->
            <WrapPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,10">
                <Button x:Name="SalesReportBtn" Content="📈 تقرير المبيعات" Width="150" Height="40"
                        Background="#28A745" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="SalesReportBtn_Click"/>

                <Button x:Name="InventoryReportBtn" Content="📦 تقرير المخزون" Width="150" Height="40"
                        Background="#17A2B8" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="InventoryReportBtn_Click"/>

                <Button x:Name="CustomersReportBtn" Content="👥 تقرير العملاء" Width="150" Height="40"
                        Background="#FFC107" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="CustomersReportBtn_Click"/>

                <Button x:Name="MaintenanceReportBtn" Content="🛠️ تقرير الصيانة" Width="150" Height="40"
                        Background="#DC3545" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="MaintenanceReportBtn_Click"/>

                <Button x:Name="FinancialReportBtn" Content="💰 التقرير المالي" Width="150" Height="40"
                        Background="#6F42C1" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="FinancialReportBtn_Click"/>

                <Button x:Name="ComprehensiveReportBtn" Content="📋 التقرير الشامل" Width="150" Height="40"
                        Background="#343A40" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="5" Click="ComprehensiveReportBtn_Click"/>
            </WrapPanel>

            <!-- أزرار الطباعة والتصدير -->
            <WrapPanel Grid.Row="1" HorizontalAlignment="Center">
                <Button x:Name="PrintReportBtn" Content="🖨️ طباعة التقرير" Width="140" Height="35"
                        Background="#495057" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="PrintReportBtn_Click" IsEnabled="False"/>

                <Button x:Name="ExportPdfBtn" Content="📄 تصدير PDF" Width="120" Height="35"
                        Background="#E74C3C" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="ExportPdfBtn_Click" IsEnabled="False"/>

                <Button x:Name="ExportExcelBtn" Content="📊 تصدير Excel" Width="120" Height="35"
                        Background="#1F7A1F" Foreground="White" BorderThickness="0"
                        FontSize="12" FontWeight="SemiBold" Margin="5" Click="ExportExcelBtn_Click" IsEnabled="False"/>
            </WrapPanel>
        </Grid>
        
        <!-- منطقة عرض التقارير -->
        <Border Grid.Row="2" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="5">
            <ScrollViewer x:Name="ReportScrollViewer" VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="ReportContent" Margin="20">
                    <!-- محتوى التقرير سيتم إضافته هنا -->
                    <TextBlock Text="اختر نوع التقرير من الأزرار أعلاه لعرض البيانات" 
                               FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center" 
                               Foreground="#6C757D" Margin="0,50,0,0"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
