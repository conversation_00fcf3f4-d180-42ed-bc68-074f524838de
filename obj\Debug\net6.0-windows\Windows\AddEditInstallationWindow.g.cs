﻿#pragma checksum "..\..\..\..\Windows\AddEditInstallationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "397623F99DCB3A5A61C405D382D56FC356FA871E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditInstallationWindow
    /// </summary>
    public partial class AddEditInstallationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 18 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SelectedDeviceTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectDeviceButton;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ManufacturerTextBox;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerComboBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker InstallationDatePicker;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarrantyYearsTextBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker WarrantyEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InstallationCostTextBox;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InstallationLocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPersonNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPersonPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPersonEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPersonPositionTextBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InstallationStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TechnicianNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EquipmentConditionComboBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SupplierCompanyTextBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InstallationNotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InstallationReportPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseInstallationReportBtn;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewInstallationReportBtn;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveInstallationReportBtn;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditinstallationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SelectedDeviceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.SelectDeviceButton = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.SelectDeviceButton.Click += new System.Windows.RoutedEventHandler(this.SelectDeviceButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ManufacturerTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CustomerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.InstallationDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.WarrantyYearsTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 123 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.WarrantyYearsTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.WarrantyYearsTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.WarrantyEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 128 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.WarrantyEndDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.WarrantyEndDatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InstallationCostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.InstallationLocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ContactPersonNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.ContactPersonPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ContactPersonEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ContactPersonPositionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.InstallationStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.TechnicianNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.EquipmentConditionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.SupplierCompanyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.InstallationNotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.InstallationReportPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.BrowseInstallationReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 267 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.BrowseInstallationReportBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseInstallationReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ViewInstallationReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.ViewInstallationReportBtn.Click += new System.Windows.RoutedEventHandler(this.ViewInstallationReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.RemoveInstallationReportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 276 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.RemoveInstallationReportBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveInstallationReportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\..\Windows\AddEditInstallationWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

