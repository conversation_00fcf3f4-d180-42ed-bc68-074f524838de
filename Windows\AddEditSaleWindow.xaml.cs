using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditSaleWindow : Window
    {
        private Sale _sale;
        private bool _isEditMode;
        
        public AddEditSaleWindow(Sale sale = null)
        {
            InitializeComponent();
            _sale = sale;
            _isEditMode = sale != null;
            
            LoadDataAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل مبيعة";
                LoadSaleData();
            }
            else
            {
                // تعيين القيم الافتراضية
                SaleDatePicker.SelectedDate = DateTime.Now;
                PaymentStatusComboBox.SelectedIndex = 0;
                GenerateInvoiceNumber();
                QuantityTextBox.Text = "1";
                DiscountPercentageTextBox.Text = "0";
            }
        }
        
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل العملاء
                var customers = await App.DatabaseContext.Customers
                    .Where(c => c.Status == "نشط")
                    .ToListAsync();
                CustomerComboBox.ItemsSource = customers;
                
                // تحميل الأجهزة المتاحة
                var devices = await App.DatabaseContext.MedicalDevices
                    .Where(d => d.Status == "متاح")
                    .ToListAsync();
                DeviceComboBox.ItemsSource = devices;
                
                if (!_isEditMode && customers.Any())
                    CustomerComboBox.SelectedIndex = 0;
                
                if (!_isEditMode && devices.Any())
                    DeviceComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void GenerateInvoiceNumber()
        {
            var invoiceNumber = $"INV-{DateTime.Now:yyyy}-{DateTime.Now:MMddHHmmss}";
            InvoiceNumberTextBox.Text = invoiceNumber;
        }
        
        private void LoadSaleData()
        {
            if (_sale == null) return;
            
            InvoiceNumberTextBox.Text = _sale.InvoiceNumber;
            SaleDatePicker.SelectedDate = _sale.SaleDate;
            QuantityTextBox.Text = _sale.Quantity.ToString();
            UnitPriceTextBox.Text = _sale.UnitPrice.ToString();
            TotalAmountTextBox.Text = _sale.TotalAmount.ToString();
            DiscountPercentageTextBox.Text = _sale.Discount.ToString();
            FinalAmountTextBox.Text = _sale.FinalAmount.ToString();
            
            // تعيين حالة الدفع
            foreach (ComboBoxItem item in PaymentStatusComboBox.Items)
            {
                if (item.Content.ToString() == _sale.PaymentStatus)
                {
                    PaymentStatusComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين العميل والجهاز
            CustomerComboBox.SelectedValue = _sale.CustomerId;
            DeviceComboBox.SelectedValue = _sale.MedicalDeviceId;
        }
        
        private void DeviceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DeviceComboBox.SelectedItem is MedicalDevice device)
            {
                UnitPriceTextBox.Text = device.SellingPrice.ToString();
                DeviceInfoTextBlock.Text = $"الجهاز: {device.Name} - الماركة: {device.Brand} - الموديل: {device.Model} - السعر: {device.SellingPrice:C}";
                CalculateTotal(null, null);
            }
            
            if (CustomerComboBox.SelectedItem is Customer customer)
            {
                CustomerInfoTextBlock.Text = $"العميل: {customer.Name} - النوع: {customer.CustomerType} - الحد الائتماني: {customer.CreditLimit:C}";
            }
        }
        
        private void CalculateTotal(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(QuantityTextBox.Text, out decimal quantity) &&
                decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice))
            {
                var totalAmount = quantity * unitPrice;
                TotalAmountTextBox.Text = totalAmount.ToString("F2");

                // حساب الخصم بالنسبة المئوية
                if (decimal.TryParse(DiscountPercentageTextBox.Text, out decimal discountPercentage))
                {
                    var discountAmount = totalAmount * (discountPercentage / 100);
                    var finalAmount = totalAmount - discountAmount;
                    FinalAmountTextBox.Text = finalAmount.ToString("F2");

                    // تحديث حسابات الدفع الجزئي إذا كانت مفعلة
                    if (PartialPaymentPanel.Visibility == Visibility.Visible)
                    {
                        CalculatePartialPayment();
                    }
                }
            }
        }

        private void CalculatePartialPayment()
        {
            try
            {
                var finalAmount = decimal.Parse(FinalAmountTextBox.Text);
                var paidAmount = string.IsNullOrEmpty(PaidAmountTextBox.Text) ? 0 : decimal.Parse(PaidAmountTextBox.Text);

                if (finalAmount > 0)
                {
                    var paymentPercentage = (paidAmount / finalAmount) * 100;
                    PaymentPercentageTextBox.Text = paymentPercentage.ToString("F1") + "%";

                    var remainingAmount = finalAmount - paidAmount;
                    RemainingAmountTextBlock.Text = $"المبلغ المتبقي: {remainingAmount:F2} ر.س";
                }
            }
            catch
            {
                PaymentPercentageTextBox.Text = "0%";
                RemainingAmountTextBlock.Text = "المبلغ المتبقي: 0 ر.س";
            }
        }

        private void PaymentStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentStatusComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var status = selectedItem.Content.ToString();
                PartialPaymentPanel.Visibility = status == "دفع جزئي" ? Visibility.Visible : Visibility.Collapsed;

                if (status == "دفع جزئي")
                {
                    PaidAmountTextBox.Text = "0";
                    CalculatePartialPayment();
                }
            }
        }

        private void SelectFromInventoryBtn_Click(object sender, RoutedEventArgs e)
        {
            var inventorySelectionWindow = new InventorySelectionWindow();
            if (inventorySelectionWindow.ShowDialog() == true)
            {
                var selectedItem = inventorySelectionWindow.SelectedInventoryItem;
                if (selectedItem != null)
                {
                    // البحث عن الجهاز المقابل في قائمة الأجهزة أو إنشاء واحد جديد
                    DeviceComboBox.Text = selectedItem.Name;
                    UnitPriceTextBox.Text = selectedItem.UnitPrice.ToString("F2");
                    QuantityTextBox.Text = "1";
                    CalculateTotal(null, null);
                }
            }
        }

        private void PaidAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculatePartialPayment();
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateSaleData();
                    App.DatabaseContext.Sales.Update(_sale);
                }
                else
                {
                    _sale = new Sale();
                    UpdateSaleData();
                    _sale.CreatedDate = DateTime.Now;
                    App.DatabaseContext.Sales.Add(_sale);
                }
                
                _sale.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث المبيعة بنجاح!" : "تم إضافة المبيعة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateSaleData()
        {
            var customer = CustomerComboBox.SelectedItem as Customer;
            var device = DeviceComboBox.SelectedItem as MedicalDevice;
            
            _sale.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
            _sale.SaleDate = SaleDatePicker.SelectedDate ?? DateTime.Now;
            _sale.CustomerId = customer.Id;
            _sale.CustomerName = customer.Name;
            _sale.MedicalDeviceId = device.Id;
            _sale.DeviceName = device.Name;
            _sale.Quantity = int.Parse(QuantityTextBox.Text);
            _sale.UnitPrice = decimal.Parse(UnitPriceTextBox.Text);
            _sale.TotalAmount = decimal.Parse(TotalAmountTextBox.Text);
            _sale.Discount = decimal.Parse(DiscountPercentageTextBox.Text);
            _sale.FinalAmount = decimal.Parse(FinalAmountTextBox.Text);
            _sale.PaymentStatus = ((ComboBoxItem)PaymentStatusComboBox.SelectedItem).Content.ToString();
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (SaleDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ البيع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (DeviceComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الجهاز الطبي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!int.TryParse(QuantityTextBox.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) || unitPrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر وحدة صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (PaymentStatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الدفع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
