using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditInventoryWindow : Window
    {
        private InventoryItem _item;
        private bool _isEditMode;
        
        public AddEditInventoryWindow(InventoryItem item = null)
        {
            InitializeComponent();
            _item = item;
            _isEditMode = item != null;

            LoadInventoryCategoriesAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل عنصر مخزون";
                LoadItemData();
            }
            else
            {
                // تعيين القيم الافتراضية
                LocationComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
                UnitComboBox.SelectedIndex = 0;
                CategoryComboBox.SelectedIndex = 0;
            }
        }
        
        private void LoadItemData()
        {
            if (_item == null) return;
            
            NameTextBox.Text = _item.Name;
            DescriptionTextBox.Text = _item.Description;
            CurrentStockTextBox.Text = _item.CurrentStock.ToString();
            MinimumStockTextBox.Text = _item.MinimumStock.ToString();
            UnitPriceTextBox.Text = _item.UnitPrice.ToString();
            ExpiryDatePicker.SelectedDate = _item.ExpiryDate;
            
            // تعيين الفئة
            foreach (var item in CategoryComboBox.Items)
            {
                if (item.ToString().Contains(_item.Category))
                {
                    CategoryComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الوحدة
            foreach (var item in UnitComboBox.Items)
            {
                if (item.ToString().Contains(_item.Unit))
                {
                    UnitComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الموقع
            foreach (var item in LocationComboBox.Items)
            {
                if (item.ToString().Contains(_item.Location))
                {
                    LocationComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (var item in StatusComboBox.Items)
            {
                if (item.ToString().Contains(_item.Status))
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateItemData();
                    App.DatabaseContext.InventoryItems.Update(_item);
                }
                else
                {
                    _item = new InventoryItem();
                    UpdateItemData();
                    _item.CreatedDate = DateTime.Now;
                    App.DatabaseContext.InventoryItems.Add(_item);
                }
                
                _item.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث العنصر بنجاح!" : "تم إضافة العنصر بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateItemData()
        {
            _item.Name = NameTextBox.Text.Trim();
            _item.Category = CategoryComboBox.Text;
            _item.Description = DescriptionTextBox.Text.Trim();
            _item.CurrentStock = int.Parse(CurrentStockTextBox.Text);
            _item.MinimumStock = int.Parse(MinimumStockTextBox.Text);
            _item.Unit = UnitComboBox.Text;
            _item.UnitPrice = decimal.Parse(UnitPriceTextBox.Text);
            _item.Location = LocationComboBox.Text;
            _item.Status = StatusComboBox.Text;
            _item.ExpiryDate = ExpiryDatePicker.SelectedDate;
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العنصر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }
            
            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }
            
            if (!int.TryParse(CurrentStockTextBox.Text, out int currentStock) || currentStock < 0)
            {
                MessageBox.Show("يرجى إدخال مخزون حالي صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrentStockTextBox.Focus();
                return false;
            }
            
            if (!int.TryParse(MinimumStockTextBox.Text, out int minimumStock) || minimumStock < 0)
            {
                MessageBox.Show("يرجى إدخال حد أدنى صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MinimumStockTextBox.Focus();
                return false;
            }
            
            if (UnitComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الوحدة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitComboBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) || unitPrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر وحدة صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitPriceTextBox.Focus();
                return false;
            }
            
            if (LocationComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموقع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                LocationComboBox.Focus();
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الحالة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async void LoadInventoryCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.InventoryCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                CategoryComboBox.Items.Clear();

                foreach (var category in categories)
                {
                    CategoryComboBox.Items.Add(category.Name);
                }

                // إضافة فئات افتراضية إذا لم توجد فئات
                if (!categories.Any())
                {
                    CategoryComboBox.Items.Add("مستلزمات طبية");
                    CategoryComboBox.Items.Add("أدوات طبية");
                    CategoryComboBox.Items.Add("مواد التعقيم");
                    CategoryComboBox.Items.Add("أدوات مختبر");
                    CategoryComboBox.Items.Add("أدوات جراحية");
                    CategoryComboBox.Items.Add("مستلزمات العناية");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فئات المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageInventoryCategoriesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageCategoriesWindow = new ManageInventoryCategoriesWindow();
            if (manageCategoriesWindow.ShowDialog() == true)
            {
                LoadInventoryCategoriesAsync();
            }
        }
    }
}
