<Window x:Class="MedicalDevicesManager.Windows.NotificationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الإشعارات والتنبيهات" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🔔 الإشعارات والتنبيهات" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#17A2B8"/>
        
        <!-- أزرار الإجراءات والفلترة -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="RefreshBtn" Content="🔄 تحديث" Width="100" Height="35" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="RefreshBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="MarkAllReadBtn" Content="✅ تحديد الكل كمقروء" Width="150" Height="35" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="MarkAllReadBtn_Click"/>
            
            <Button Grid.Column="2" x:Name="CheckSystemBtn" Content="🔍 فحص النظام" Width="120" Height="35" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="CheckSystemBtn_Click"/>
            
            <TextBlock Grid.Column="4" Text="فلترة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="5" x:Name="FilterComboBox" Width="150" Height="35" 
                      SelectionChanged="FilterComboBox_SelectionChanged">
                <ComboBoxItem Content="جميع الإشعارات" IsSelected="True"/>
                <ComboBoxItem Content="غير مقروءة فقط"/>
                <ComboBoxItem Content="تنبيهات المخزون"/>
                <ComboBoxItem Content="تنبيهات الصيانة"/>
                <ComboBoxItem Content="تنبيهات انتهاء الصلاحية"/>
                <ComboBoxItem Content="النسخ الاحتياطي"/>
                <ComboBoxItem Content="التصدير"/>
            </ComboBox>
        </Grid>
        
        <!-- جدول الإشعارات -->
        <DataGrid Grid.Row="2" x:Name="NotificationsDataGrid" 
                  AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  AlternatingRowBackground="#F8F9FA" FontSize="14"
                  SelectionChanged="NotificationsDataGrid_SelectionChanged">
            
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="الحالة" Width="60">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Width="20" Height="20" CornerRadius="10" HorizontalAlignment="Center">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsRead}" Value="False">
                                                <Setter Property="Background" Value="#DC3545"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding IsRead}" Value="True">
                                                <Setter Property="Background" Value="#28A745"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="الأولوية" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Priority}" HorizontalAlignment="Center" FontWeight="Bold">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Priority}" Value="Critical">
                                                <Setter Property="Foreground" Value="#DC3545"/>
                                                <Setter Property="Text" Value="🔴 حرج"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Priority}" Value="High">
                                                <Setter Property="Foreground" Value="#FD7E14"/>
                                                <Setter Property="Text" Value="🟠 عالي"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Priority}" Value="Medium">
                                                <Setter Property="Foreground" Value="#FFC107"/>
                                                <Setter Property="Text" Value="🟡 متوسط"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Priority}" Value="Low">
                                                <Setter Property="Foreground" Value="#28A745"/>
                                                <Setter Property="Text" Value="🟢 منخفض"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="200"/>
                <DataGridTextColumn Header="الرسالة" Binding="{Binding Message}" Width="300"/>
                <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="80"/>
                <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="100"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" Width="120"/>
                
                <!-- عمود الإجراءات -->
                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="👁️" Width="30" Height="25" 
                                        Background="#17A2B8" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="تحديد كمقروء"
                                        Click="MarkReadBtn_Click"/>
                                <Button Content="🗑️" Width="30" Height="25" 
                                        Background="#DC3545" Foreground="White" BorderThickness="0" 
                                        Margin="2,0,2,0" Cursor="Hand" ToolTip="حذف"
                                        Click="DeleteNotificationBtn_Click"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- معلومات الإشعار المحدد -->
        <Border Grid.Row="3" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="15" Margin="0,20,0,0" x:Name="NotificationDetailsPanel" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="📋 تفاصيل الإشعار" FontWeight="Bold" Margin="0,0,0,10" Foreground="#1976D2"/>
                <TextBlock x:Name="NotificationDetailsTextBlock" Text="" FontSize="12" TextWrapping="Wrap"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <Button x:Name="ViewRelatedItemBtn" Content="🔗 عرض العنصر المرتبط" Width="150" Height="30" 
                            Background="#17A2B8" Foreground="White" BorderThickness="0" 
                            FontSize="12" FontWeight="SemiBold" Margin="0,0,10,0" 
                            Click="ViewRelatedItemBtn_Click" Visibility="Collapsed"/>
                    
                    <TextBlock x:Name="UnreadCountTextBlock" Text="" VerticalAlignment="Center" 
                               FontWeight="Bold" Foreground="#DC3545" Margin="20,0,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
