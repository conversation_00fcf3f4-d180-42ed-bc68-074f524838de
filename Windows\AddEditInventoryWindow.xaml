<Window x:Class="MedicalDevicesManager.Windows.AddEditInventoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل عنصر مخزون" Height="600" Width="500"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة عنصر مخزون جديد" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم العنصر -->
                <TextBlock Text="اسم العنصر *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الفئة -->
                <TextBlock Text="الفئة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0" x:Name="CategoryComboBox" Height="35" Padding="10,8" FontSize="14"
                              IsEditable="True" Margin="0,0,5,0"/>

                    <Button Grid.Column="1" x:Name="ManageInventoryCategoriesBtn" Content="⚙️" Width="35" Height="35"
                            Background="#6C757D" Foreground="White" BorderThickness="0"
                            ToolTip="إدارة فئات المخزون" Click="ManageInventoryCategoriesBtn_Click"/>
                </Grid>
                
                <!-- الوصف -->
                <TextBlock Text="الوصف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="60" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- المخزون والحد الأدنى -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المخزون الحالي *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CurrentStockTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الحد الأدنى *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="MinimumStockTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- الوحدة وسعر الوحدة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الوحدة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="UnitComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="صندوق"/>
                            <ComboBoxItem Content="علبة"/>
                            <ComboBoxItem Content="زجاجة"/>
                            <ComboBoxItem Content="كيس"/>
                            <ComboBoxItem Content="قطعة"/>
                            <ComboBoxItem Content="جهاز"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="سعر الوحدة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="UnitPriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- الموقع والحالة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الموقع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="LocationComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="المستودع الرئيسي"/>
                            <ComboBoxItem Content="المستودع الثانوي"/>
                            <ComboBoxItem Content="المعرض"/>
                            <ComboBoxItem Content="قيد التسليم"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الحالة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="متاح"/>
                            <ComboBoxItem Content="مخزون منخفض"/>
                            <ComboBoxItem Content="نفد المخزون"/>
                            <ComboBoxItem Content="معلق"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
                
                <!-- تاريخ الانتهاء -->
                <TextBlock Text="تاريخ الانتهاء (اختياري)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="ExpiryDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ" Width="120" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
