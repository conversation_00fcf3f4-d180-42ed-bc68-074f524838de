<Window x:Class="MedicalDevicesManager.Windows.AddEditDeviceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل جهاز طبي" Height="700" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة جهاز طبي جديد" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم الجهاز -->
                <TextBlock Text="اسم الجهاز *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الماركة -->
                <TextBlock Text="الماركة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="BrandTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الموديل -->
                <TextBlock Text="الموديل *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="ModelTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الأرقام التسلسلية -->
                <TextBlock Text="الأرقام التسلسلية *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBlock Text="💡 سيتم إنشاء جهاز منفصل لكل رقم تسلسلي مضاف"
                          FontSize="11" Foreground="#6C757D" Margin="0,0,0,8"/>
                <Border BorderBrush="#CED4DA" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="200"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- نموذج إضافة رقم تسلسلي -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- تسميات الحقول -->
                            <Grid Grid.Row="0" Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="الرقم التسلسلي" FontSize="11"
                                          FontWeight="SemiBold" Margin="0,0,5,0"/>
                                <TextBlock Grid.Column="1" Text="اسم المكون" FontSize="11"
                                          FontWeight="SemiBold" Margin="5,0,5,0"/>
                            </Grid>

                            <!-- حقول الإدخال -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox Grid.Column="0" x:Name="NewSerialNumberTextBox" Height="30" Padding="8,5"
                                         FontSize="12" Margin="0,0,5,0"/>
                                <TextBox Grid.Column="1" x:Name="NewComponentNameTextBox" Height="30" Padding="8,5"
                                         FontSize="12" Margin="5,0,5,0"/>
                                <Button Grid.Column="2" x:Name="AddSerialBtn" Content="➕" Width="30" Height="30"
                                        Background="#28A745" Foreground="White" BorderThickness="0"
                                        FontSize="14" FontWeight="Bold" Click="AddSerialBtn_Click"
                                        ToolTip="إضافة رقم تسلسلي"/>
                            </Grid>
                        </Grid>

                        <!-- جدول الأرقام التسلسلية -->
                        <DataGrid Grid.Row="1" x:Name="SerialNumbersDataGrid"
                                  AutoGenerateColumns="False" CanUserAddRows="False"
                                  CanUserDeleteRows="False" IsReadOnly="True"
                                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                  Background="White" RowBackground="White"
                                  AlternatingRowBackground="#F8F9FA"
                                  BorderThickness="0" FontSize="11"
                                  SelectionMode="Single" SelectionUnit="FullRow">

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الرقم التسلسلي" Binding="{Binding SerialNumber}" Width="*"/>
                                <DataGridTextColumn Header="اسم المكون" Binding="{Binding ComponentName}" Width="*"/>
                                <DataGridTemplateColumn Header="حذف" Width="50">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="🗑️" Width="25" Height="25"
                                                    Background="#DC3545" Foreground="White"
                                                    BorderThickness="0" FontSize="10"
                                                    Click="RemoveSerialBtn_Click"
                                                    ToolTip="حذف"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- معلومات إضافية -->
                        <TextBlock Grid.Row="2" x:Name="SerialCountTextBlock" Text="العدد: 0"
                                   FontSize="11" Foreground="#6C757D" Margin="0,5,0,0"/>
                    </Grid>
                </Border>
                
                <!-- الفئة -->
                <TextBlock Text="الفئة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0" x:Name="CategoryComboBox" Height="35" Padding="10,8" FontSize="14"
                              IsEditable="True" Margin="0,0,5,0"/>

                    <Button Grid.Column="1" x:Name="ManageCategoriesBtn" Content="⚙️" Width="35" Height="35"
                            Background="#6C757D" Foreground="White" BorderThickness="0"
                            ToolTip="إدارة الفئات" Click="ManageCategoriesBtn_Click"/>
                </Grid>
                
                <!-- الوصف -->
                <TextBlock Text="الوصف" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="80" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- الأسعار -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="سعر الشراء *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PurchasePriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="سعر البيع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="SellingPriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- التواريخ -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="تاريخ الشراء *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="PurchaseDatePicker" Height="35" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="بداية الضمان *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="WarrantyStartDatePicker" Height="35" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- نهاية الضمان -->
                <TextBlock Text="نهاية الضمان *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="WarrantyEndDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- المورد -->
                <TextBlock Text="المورد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="SupplierComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الموقع والحالة -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الموقع *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="LocationComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="المستودع الرئيسي"/>
                            <ComboBoxItem Content="المستودع الثانوي"/>
                            <ComboBoxItem Content="المعرض"/>
                            <ComboBoxItem Content="قيد التسليم"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الحالة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14">
                            <ComboBoxItem Content="متاح"/>
                            <ComboBoxItem Content="مباع"/>
                            <ComboBoxItem Content="تحت الصيانة"/>
                            <ComboBoxItem Content="معطل"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- المستندات والملفات -->
                <Border Background="#E3F2FD" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📄 المستندات والملفات" FontWeight="Bold" FontSize="14" Margin="0,0,0,15"/>

                        <!-- كتيب المستخدم -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="كتيب المستخدم:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="UserManualPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseUserManualBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseUserManualBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearUserManualBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearUserManualBtn_Click"/>
                        </Grid>

                        <!-- كتيب الصيانة -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="كتيب الصيانة:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="MaintenanceManualPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseMaintenanceManualBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseMaintenanceManualBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearMaintenanceManualBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearMaintenanceManualBtn_Click"/>
                        </Grid>

                        <!-- شهادة المنشأ -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="شهادة المنشأ:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="OriginCertificatePathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseOriginCertificateBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseOriginCertificateBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearOriginCertificateBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearOriginCertificateBtn_Click"/>
                        </Grid>

                        <!-- أوراق شهادة الجودة -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق شهادة الجودة:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="QualityCertificatePathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseQualityCertificateBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseQualityCertificateBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearQualityCertificateBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearQualityCertificateBtn_Click"/>
                        </Grid>

                        <!-- المصادقات الرسمية -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="المصادقات الرسمية:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="OfficialCertificationsPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseOfficialCertificationsBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseOfficialCertificationsBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearOfficialCertificationsBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearOfficialCertificationsBtn_Click"/>
                        </Grid>

                        <!-- كتيب المعلومات التقنية -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="كتيب المعلومات التقنية:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="TechnicalInfoBookletPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseTechnicalInfoBookletBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseTechnicalInfoBookletBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearTechnicalInfoBookletBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearTechnicalInfoBookletBtn_Click"/>
                        </Grid>

                        <!-- أوراق الاستيراد -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق الاستيراد:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="ImportPapersPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseImportPapersBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseImportPapersBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearImportPapersBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearImportPapersBtn_Click"/>
                        </Grid>

                        <!-- أوراق العقود -->
                        <Grid Margin="0,0,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="أوراق العقود:" VerticalAlignment="Center" FontSize="12"/>
                            <TextBox Grid.Column="1" x:Name="ContractPapersPathTextBox" Height="30" Padding="5"
                                     FontSize="12" IsReadOnly="True" Margin="5,0,5,0"/>
                            <Button Grid.Column="2" x:Name="BrowseContractPapersBtn" Content="📁" Width="30" Height="30"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0"
                                    ToolTip="اختيار ملف" Click="BrowseContractPapersBtn_Click"/>
                            <Button Grid.Column="3" x:Name="ClearContractPapersBtn" Content="❌" Width="30" Height="30"
                                    Background="#F44336" Foreground="White" BorderThickness="0"
                                    ToolTip="مسح الملف" Margin="2,0,0,0" Click="ClearContractPapersBtn_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ" Width="120" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
