using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class InventorySelectionWindow : Window
    {
        public InventoryItem SelectedInventoryItem { get; private set; }
        private List<InventoryItem> _allInventoryItems;
        
        public InventorySelectionWindow()
        {
            InitializeComponent();
            LoadInventoryDataAsync();
            LoadCategoriesAsync();
        }
        
        private async void LoadInventoryDataAsync()
        {
            try
            {
                _allInventoryItems = await App.DatabaseContext.InventoryItems
                    .Where(i => i.Status == "متاح" && i.CurrentStock > 0)
                    .OrderBy(i => i.Name)
                    .ToListAsync();
                    
                InventoryDataGrid.ItemsSource = _allInventoryItems;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.InventoryCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
                
                CategoryFilterComboBox.Items.Clear();
                CategoryFilterComboBox.Items.Add("جميع الفئات");
                
                foreach (var category in categories)
                {
                    CategoryFilterComboBox.Items.Add(category.Name);
                }
                
                CategoryFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }
        
        private void CategoryFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }
        
        private void ApplyFilters()
        {
            if (_allInventoryItems == null) return;
            
            var filteredItems = _allInventoryItems.AsEnumerable();
            
            // فلترة بالبحث
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredItems = filteredItems.Where(i => 
                    i.Name.ToLower().Contains(searchTerm) ||
                    i.Description.ToLower().Contains(searchTerm) ||
                    i.Category.ToLower().Contains(searchTerm));
            }
            
            // فلترة بالفئة
            if (CategoryFilterComboBox.SelectedItem != null && 
                CategoryFilterComboBox.SelectedItem.ToString() != "جميع الفئات")
            {
                var selectedCategory = CategoryFilterComboBox.SelectedItem.ToString();
                filteredItems = filteredItems.Where(i => i.Category == selectedCategory);
            }
            
            InventoryDataGrid.ItemsSource = filteredItems.ToList();
        }
        
        private void ClearFiltersBtn_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            CategoryFilterComboBox.SelectedIndex = 0;
            InventoryDataGrid.ItemsSource = _allInventoryItems;
        }
        
        private void InventoryDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SelectCurrentItem();
        }
        
        private void SelectItemBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as InventoryItem;
            
            if (item != null)
            {
                InventoryDataGrid.SelectedItem = item;
                SelectCurrentItem();
            }
        }
        
        private void SelectBtn_Click(object sender, RoutedEventArgs e)
        {
            SelectCurrentItem();
        }
        
        private void SelectCurrentItem()
        {
            if (InventoryDataGrid.SelectedItem is InventoryItem selectedItem)
            {
                SelectedInventoryItem = selectedItem;
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى اختيار عنصر من المخزون أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
        
        private void InventoryDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InventoryDataGrid.SelectedItem is InventoryItem selectedItem)
            {
                SelectBtn.IsEnabled = true;
                SelectedItemPanel.Visibility = Visibility.Visible;
                
                SelectedItemInfoTextBlock.Text = $"الاسم: {selectedItem.Name}\n" +
                                               $"الفئة: {selectedItem.Category}\n" +
                                               $"الوصف: {selectedItem.Description}\n" +
                                               $"المخزون الحالي: {selectedItem.CurrentStock}\n" +
                                               $"سعر الوحدة: {selectedItem.UnitPrice:C}\n" +
                                               $"الحالة: {selectedItem.Status}";
            }
            else
            {
                SelectBtn.IsEnabled = false;
                SelectedItemPanel.Visibility = Visibility.Collapsed;
            }
        }
    }
}
