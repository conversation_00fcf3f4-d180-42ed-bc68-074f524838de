@echo off
echo ========================================
echo Database Reset Tool - Medical Devices Management System v6.0
echo ========================================
echo.
echo This tool will:
echo [1] Delete the existing database
echo [2] Create a new database with updated schema
echo [3] Load default categories and sample data
echo.
echo WARNING: This will delete all existing data!
echo.
set /p confirm="Are you sure you want to continue? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Operation cancelled.
    pause
    exit /b
)

echo.
echo [1/3] Deleting existing database...
if exist "MedicalDevicesIntegrated.db" (
    del "MedicalDevicesIntegrated.db"
    echo Database deleted successfully.
) else (
    echo No existing database found.
)

echo.
echo [2/3] Building the application...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please check for errors.
    pause
    exit /b
)

echo.
echo [3/3] Starting the application to create new database...
echo The application will start and create a new database with:
echo - Updated schema for categories and documents
echo - 8 default device categories
echo - Sample data for testing
echo.
echo Please close the application after it loads successfully.
echo.
pause
dotnet run
echo.
echo Database reset completed!
echo The new database includes:
echo - DeviceCategories table with 8 default categories
echo - Updated MedicalDevices table with document fields
echo - All sample data for testing
echo.
pause
