<Window x:Class="MedicalDevicesManager.Windows.AddEditSparePartWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل قطعة غيار أو ملحق" Height="700" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        FlowDirection="RightToLeft" FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20">
            <TextBlock x:Name="TitleBlock" Text="إضافة قطعة غيار أو ملحق جديد" 
                       FontSize="24" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30">
                <!-- اسم القطعة -->
                <TextBlock Text="اسم القطعة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- اسم الجهاز المرتبط -->
                <TextBlock Text="اسم الجهاز المرتبط *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="AssociatedDeviceComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"
                          DisplayMemberPath="Name" SelectedValuePath="Name" IsEditable="True"/>
                
                <!-- العدد المتوفر -->
                <TextBlock Text="العدد المتوفر *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="AvailableQuantityTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- الأسعار -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="سعر الشراء *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PurchasePriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="سعر البيع مفرد *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="IndividualSellingPriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="4">
                        <TextBlock Text="سعر البيع جملة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="WholesaleSellingPriceTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- تاريخ الشراء -->
                <TextBlock Text="تاريخ الشراء *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="PurchaseDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- موقع القطعة في مخزن الورشة -->
                <TextBlock Text="موقع القطعة في مخزن الورشة" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="WorkshopStorageLocationTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- معلومات التاريخ -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات التاريخ" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="تاريخ الإنشاء:" FontWeight="SemiBold" FontSize="12"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="-" FontSize="12" Foreground="#6C757D"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="آخر تحديث:" FontWeight="SemiBold" FontSize="12"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="-" FontSize="12" Foreground="#6C757D"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ" Width="120" Height="40" 
                        Background="#28A745" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
                
                <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40"
                        Background="#6C757D" Foreground="White" BorderThickness="0"
                        FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
