using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageCustomerTypesWindow : Window
    {
        private CustomerType _editingType = null;
        
        public ManageCustomerTypesWindow()
        {
            InitializeComponent();
            LoadTypesAsync();
        }
        
        private async void LoadTypesAsync()
        {
            try
            {
                var types = await App.DatabaseContext.CustomerTypes
                    .OrderBy(t => t.Name)
                    .ToListAsync();
                    
                TypesDataGrid.ItemsSource = types;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أنواع العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddTypeBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _editingType = null;
            TypeNameTextBox.Focus();
        }
        
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadTypesAsync();
            ClearForm();
        }
        
        private void EditTypeBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var type = button?.DataContext as CustomerType;
            
            if (type != null)
            {
                _editingType = type;
                LoadTypeToForm(type);
            }
        }
        
        private async void DeleteTypeBtn_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var type = button?.DataContext as CustomerType;
            
            if (type != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف نوع العميل '{type.Name}'؟\n\nسيتم تحويل جميع العملاء من هذا النوع إلى 'غير محدد'.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // تحديث العملاء الذين يستخدمون هذا النوع
                        var customersWithType = await App.DatabaseContext.Customers
                            .Where(c => c.CustomerType == type.Name)
                            .ToListAsync();
                            
                        foreach (var customer in customersWithType)
                        {
                            customer.CustomerType = "غير محدد";
                        }
                        
                        App.DatabaseContext.CustomerTypes.Remove(type);
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف النوع بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadTypesAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف النوع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private async void SaveTypeBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingType == null)
                {
                    // إضافة نوع جديد
                    var newType = new CustomerType
                    {
                        Name = TypeNameTextBox.Text.Trim(),
                        Description = TypeDescriptionTextBox.Text.Trim(),
                        IsActive = IsActiveCheckBox.IsChecked ?? true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.CustomerTypes.Add(newType);
                }
                else
                {
                    // تعديل نوع موجود
                    var oldName = _editingType.Name;
                    
                    _editingType.Name = TypeNameTextBox.Text.Trim();
                    _editingType.Description = TypeDescriptionTextBox.Text.Trim();
                    _editingType.IsActive = IsActiveCheckBox.IsChecked ?? true;
                    _editingType.LastUpdated = DateTime.Now;
                    
                    // تحديث العملاء الذين يستخدمون هذا النوع
                    if (oldName != _editingType.Name)
                    {
                        var customersWithType = await App.DatabaseContext.Customers
                            .Where(c => c.CustomerType == oldName)
                            .ToListAsync();
                            
                        foreach (var customer in customersWithType)
                        {
                            customer.CustomerType = _editingType.Name;
                        }
                    }
                    
                    App.DatabaseContext.CustomerTypes.Update(_editingType);
                }
                
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _editingType == null ? "تم إضافة النوع بنجاح!" : "تم تحديث النوع بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                LoadTypesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ النوع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void LoadTypeToForm(CustomerType type)
        {
            TypeNameTextBox.Text = type.Name;
            TypeDescriptionTextBox.Text = type.Description;
            IsActiveCheckBox.IsChecked = type.IsActive;
        }
        
        private void ClearForm()
        {
            _editingType = null;
            TypeNameTextBox.Text = "";
            TypeDescriptionTextBox.Text = "";
            IsActiveCheckBox.IsChecked = true;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(TypeNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم النوع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeNameTextBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
